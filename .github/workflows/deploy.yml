name: Deploy

on:
  push:
    branches:
      - main
      - dev
  pull_request:
    branches:
      - main
      - dev

jobs:
  check_skip:
    runs-on: ubuntu-24.04
    outputs:
      skip: ${{ steps.skip_check.outputs.skip }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2
      - id: skip_check
        run: |
          if git log -1 --pretty=%B | grep -iE '\[noci\]|noci'; then
            echo "skip=true" >> $GITHUB_OUTPUT
          else
            echo "skip=false" >> $GITHUB_OUTPUT
          fi

  deploy:
    needs: check_skip
    if: |
      needs.check_skip.outputs.skip != 'true' && 
      (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/dev')
    runs-on: ubuntu-24.04
    environment: ${{ github.ref == 'refs/heads/main' && 'production' || 'staging' }}

    steps:
    - uses: actions/checkout@v4

    - name: Set up SSH key
      env:
        SSH_AUTH_SOCK: /tmp/ssh_agent.sock
      run: |
        mkdir -p ~/.ssh
        echo "${{ secrets.SSH_PRIVATE_KEY }}" | base64 --decode > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -p 65533 -H ************** >> ~/.ssh/known_hosts

    - name: Deploy to Server
      env:
        DEPLOY_HOST: **************
        DEPLOY_USER: root
        SSH_PORT: 65533
      run: |
        set -e
        set -o pipefail
        trap 'echo "Error occurred. Exiting..."; exit 1' ERR

        echo "🚀 Starting deployment to ${GITHUB_REF#refs/heads/} environment..."

        ssh -p $SSH_PORT -t -o StrictHostKeyChecking=no $DEPLOY_USER@$DEPLOY_HOST << 'EOF'
          set -e
          echo "📦 Navigating to project directory..."
          cd /var/www/Back-End/

          echo "🌿 Activating the virtual environment..."
          source venv/bin/activate

          echo "📥 Pulling latest changes..."
          git pull origin ${GITHUB_REF#refs/heads/} -f

          echo "📦 Installing/updating dependencies..."
          pip install -r requirements.txt

          echo "🔄 Running migrations..."
          python3 manage.py makemigrations
          python3 manage.py migrate
          python3 manage.py collectstatic --noinput

          echo "🔄 Restarting services..."
          sudo systemctl restart hypercorn.service
          sudo systemctl restart celery-worker.service

          echo "✅ Verifying services..."
          sleep 5
          if ! systemctl is-active --quiet hypercorn.service; then
            echo "❌ Hypercorn service failed to start"
            exit 1
          fi
          if ! systemctl is-active --quiet celery-worker.service; then
            echo "❌ Celery service failed to start"
            exit 1
          fi
        EOF

    - name: Notify on failure
      if: failure()
      run: |
        echo "::error::Deployment failed! Check the logs for details."