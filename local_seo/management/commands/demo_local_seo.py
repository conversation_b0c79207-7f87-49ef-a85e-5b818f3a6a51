from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from local_seo.models import BusinessProfile, LocalKeyword
from local_seo.services.audit_service import LocalSEOAuditService

User = get_user_model()


class Command(BaseCommand):
    help = 'Demonstrate Local SEO functionality with sample data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--email',
            type=str,
            help='Email of the user to create demo data for',
            default='<EMAIL>'
        )

    def handle(self, *args, **options):
        email = options['email']
        
        # Create or get demo user
        user, created = User.objects.get_or_create(
            email=email,
            defaults={
                'first_name': 'Demo',
                'last_name': 'User',
                'is_active': True,
                'is_verified': True
            }
        )
        
        if created:
            user.set_password('demo123')
            user.save()
            self.stdout.write(
                self.style.SUCCESS(f'Created demo user: {email}')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'Using existing user: {email}')
            )

        # Create demo business profile
        business_profile, created = BusinessProfile.objects.get_or_create(
            user=user,
            business_name='Demo Restaurant Sydney',
            defaults={
                'address_line_1': '123 George Street',
                'city': 'Sydney',
                'state': 'NSW',
                'postal_code': '2000',
                'country': 'Australia',
                'phone_number': '+61 2 9876 5432',
                'website_url': 'https://demorestaurant.com.au',
                'business_type': 'restaurant',
                'primary_category': 'Restaurant',
                'additional_categories': ['Italian Restaurant', 'Fine Dining'],
                'business_hours': {
                    'monday': {'open': '17:00', 'close': '22:00'},
                    'tuesday': {'open': '17:00', 'close': '22:00'},
                    'wednesday': {'open': '17:00', 'close': '22:00'},
                    'thursday': {'open': '17:00', 'close': '22:00'},
                    'friday': {'open': '17:00', 'close': '23:00'},
                    'saturday': {'open': '17:00', 'close': '23:00'},
                    'sunday': {'closed': True}
                },
                'total_reviews': 127,
                'average_rating': 4.3,
                'is_verified': True,
                'status': 'active'
            }
        )

        if created:
            self.stdout.write(
                self.style.SUCCESS(f'Created demo business profile: {business_profile.business_name}')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'Using existing business profile: {business_profile.business_name}')
            )

        # Create demo keywords
        demo_keywords = [
            {
                'keyword': 'italian restaurant sydney',
                'target_location': 'Sydney, NSW, Australia',
                'search_volume': 2400,
                'competition_level': 'high',
                'intent': 'local'
            },
            {
                'keyword': 'fine dining sydney',
                'target_location': 'Sydney, NSW, Australia',
                'search_volume': 1800,
                'competition_level': 'medium',
                'intent': 'local'
            },
            {
                'keyword': 'restaurant george street',
                'target_location': 'Sydney, NSW, Australia',
                'search_volume': 800,
                'competition_level': 'low',
                'intent': 'local'
            }
        ]

        for keyword_data in demo_keywords:
            keyword, created = LocalKeyword.objects.get_or_create(
                business_profile=business_profile,
                keyword=keyword_data['keyword'],
                target_location=keyword_data['target_location'],
                defaults=keyword_data
            )
            
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Created keyword: {keyword.keyword}')
                )

        # Run a demo audit
        self.stdout.write(
            self.style.WARNING('Running Local SEO audit (this may take a moment)...')
        )
        
        try:
            audit_service = LocalSEOAuditService()
            audit_report = audit_service.conduct_full_audit(business_profile)
            
            self.stdout.write(
                self.style.SUCCESS(f'✅ Audit completed successfully!')
            )
            self.stdout.write(
                self.style.SUCCESS(f'Overall Score: {audit_report.overall_score}/100')
            )
            self.stdout.write(
                self.style.SUCCESS(f'NAP Consistency: {audit_report.nap_consistency_score}/100')
            )
            self.stdout.write(
                self.style.SUCCESS(f'Profile Completeness: {audit_report.profile_completeness_score}/100')
            )
            self.stdout.write(
                self.style.SUCCESS(f'Review Health: {audit_report.review_health_score}/100')
            )
            self.stdout.write(
                self.style.SUCCESS(f'Local Visibility: {audit_report.local_visibility_score}/100')
            )
            
            if audit_report.ai_summary:
                self.stdout.write(
                    self.style.SUCCESS(f'\nAI Summary:\n{audit_report.ai_summary}')
                )
            
            # Show recommendations
            if audit_report.recommendations:
                self.stdout.write(
                    self.style.WARNING('\n📋 Recommendations:')
                )
                for i, rec in enumerate(audit_report.recommendations[:3], 1):
                    self.stdout.write(
                        f"{i}. {rec.get('title', 'N/A')} (Priority: {rec.get('priority', 'N/A')})"
                    )
            
            # Show tasks created
            tasks_count = business_profile.seo_tasks.count()
            self.stdout.write(
                self.style.SUCCESS(f'\n📝 Created {tasks_count} SEO tasks')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Audit failed: {str(e)}')
            )

        # Display API endpoints
        self.stdout.write(
            self.style.SUCCESS('\n🚀 Local SEO API Endpoints:')
        )
        
        endpoints = [
            'GET /api/local-seo/profiles/ - List business profiles',
            'POST /api/local-seo/profiles/ - Create business profile',
            f'POST /api/local-seo/profiles/{business_profile.id}/audit/ - Run audit',
            f'GET /api/local-seo/profiles/{business_profile.id}/audits/ - List audits',
            f'GET /api/local-seo/profiles/{business_profile.id}/keywords/ - List keywords',
            f'GET /api/local-seo/profiles/{business_profile.id}/tasks/ - List SEO tasks',
            'GET /api/local-seo/dashboard/ - Dashboard overview'
        ]
        
        for endpoint in endpoints:
            self.stdout.write(f'  • {endpoint}')

        self.stdout.write(
            self.style.SUCCESS(f'\n✨ Demo completed! User: {email}, Business: {business_profile.business_name}')
        )
