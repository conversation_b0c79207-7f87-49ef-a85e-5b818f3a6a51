# Local SEO Intelligence Module

A comprehensive Local SEO intelligence module for SEOAnalyser.com.au, enabling Australian businesses and agencies to monitor, audit, and improve their local search visibility on Google through automation, insights, and actionable reporting.

## 🚀 Features

### Core Local SEO Features

#### 🔍 Google Business Profile (GBP) Audit
- **NAP Consistency Analysis**: Automated checking of Name, Address, Phone consistency
- **Profile Completeness Scoring**: Weighted scoring system for business profile completeness
- **Business Hours & Category Validation**: Verification of business information accuracy
- **Media & Photo Analysis**: Assessment of visual content completeness

#### 📈 Local Keyword Visibility & Geo-Rank Tracking
- **Geo-targeted Keyword Tracking**: Monitor rankings for location-specific keywords
- **Local Pack Monitoring**: Track presence in Google's local 3-pack results
- **Organic vs Local Rankings**: Separate tracking for organic and local search results
- **Historical Trend Analysis**: Visual trendlines for ranking performance over time

#### 🏢 Local Competitor Analysis
- **Automated Competitor Discovery**: Find competitors using Google Places API
- **Competitive Benchmarking**: Compare reviews, ratings, and profile completeness
- **Distance-based Analysis**: Analyze competitors within specified radius
- **Performance Gap Identification**: Identify areas where competitors outperform

#### ⭐️ Review Intelligence & Sentiment Analysis
- **AI-Powered Sentiment Analysis**: OpenAI GPT-4 powered review sentiment analysis
- **Topic Extraction**: Automatic identification of key themes in reviews
- **Review Health Scoring**: Comprehensive scoring based on volume, rating, and sentiment
- **Response Rate Tracking**: Monitor business response rates to reviews

#### 🧠 AI-Generated Local SEO Recommendations
- **Customized Action Plans**: GPT-4 generated, business-specific recommendations
- **Priority-based Task Management**: Automated task creation with priority levels
- **Impact Estimation**: Estimated impact scores for each recommendation
- **Plain-English Explanations**: Non-technical summaries for business owners

### Advanced Features

#### 📊 Local SEO Dashboard & Reporting
- **Visual KPI Dashboard**: Local visibility score, NAP accuracy, keyword rankings
- **Multi-location Support**: Manage multiple business locations from single dashboard
- **Exportable Reports**: PDF and CSV export capabilities
- **Historical Performance Tracking**: Long-term trend analysis

#### ✅ Task & Resolution Tracker
- **Audit-based Task Creation**: Automatic task generation from audit results
- **Progress Tracking**: Mark tasks as pending, in-progress, completed, or ignored
- **Time-stamped History**: Complete audit and task history
- **Assignment Management**: Assign tasks to team members

#### 🔔 Alerts & Notifications
- **Review Monitoring**: Alerts for new negative reviews
- **Ranking Drop Alerts**: Notifications for significant keyword ranking changes
- **Profile Change Detection**: Alerts for Google Business Profile modifications
- **Email & Dashboard Notifications**: Multiple notification channels

#### ⏰ Scheduled Background Scans
- **Automated Daily Syncs**: Daily Google Business Profile synchronization
- **Weekly Keyword Tracking**: Automated weekly ranking checks
- **Monthly Audit Reports**: Comprehensive monthly audit generation
- **Smart Delta Tracking**: Only surface meaningful changes

## 🛠 Technical Architecture

### Backend Components
- **Django 5.x + DRF**: RESTful API architecture
- **PostgreSQL**: Primary database with JSON field support
- **Celery + Redis**: Background task processing
- **OpenAI GPT-4**: AI-powered analysis and recommendations

### API Integrations
- **Google Business Profile API**: Business profile data and reviews
- **Google Places API**: Competitor discovery and business search
- **Google Maps API**: Location and distance calculations
- **DataForSEO API**: SERP tracking and keyword ranking (optional)
- **OpenAI API**: Sentiment analysis and recommendation generation

### Database Models
- **BusinessProfile**: Core business information and metrics
- **LocalKeyword**: Keyword tracking configuration
- **KeywordRanking**: Historical ranking data
- **CompetitorProfile**: Competitor business information
- **Review**: Review data with AI analysis
- **LocalAuditReport**: Comprehensive audit results
- **SentimentTag**: AI-extracted sentiment topics
- **LocalSEOTask**: Action items and task management

## 🚀 Quick Start

### 1. Installation

The Local SEO module is already integrated into the SEOAnalyser project. Ensure you have the required dependencies:

```bash
pip install openai googlemaps
```

### 2. Configuration

Add the following to your `.env` file:

```env
# OpenAI API Key for Local SEO AI features
OPENAI_API_KEY=your_openai_api_key

# Google Business Profile API settings
GOOGLE_BUSINESS_PROFILE_API_KEY=your_google_api_key
GOOGLE_API_KEY=your_google_api_key
```

### 3. Database Migration

```bash
python manage.py migrate
```

### 4. Demo Setup

Run the demo command to create sample data:

```bash
python manage.py demo_local_seo --email=<EMAIL>
```

## 📡 API Endpoints

### Business Profiles
- `GET /api/local-seo/profiles/` - List business profiles
- `POST /api/local-seo/profiles/` - Create business profile
- `GET /api/local-seo/profiles/{id}/` - Get business profile details
- `PUT /api/local-seo/profiles/{id}/` - Update business profile
- `DELETE /api/local-seo/profiles/{id}/` - Delete business profile

### Local SEO Audits
- `POST /api/local-seo/profiles/{id}/audit/` - Run comprehensive audit
- `GET /api/local-seo/profiles/{id}/audits/` - List audit reports
- `GET /api/local-seo/audits/{id}/` - Get detailed audit report

### Keyword Tracking
- `GET /api/local-seo/profiles/{id}/keywords/` - List keywords
- `POST /api/local-seo/profiles/{id}/keywords/` - Add keyword
- `POST /api/local-seo/keywords/{id}/track/` - Track keyword rankings
- `GET /api/local-seo/keywords/{id}/history/` - Get ranking history

### Competitor Analysis
- `POST /api/local-seo/profiles/{id}/competitors/` - Analyze competitors

### Review Analysis
- `POST /api/local-seo/profiles/{id}/reviews/` - Analyze reviews

### SEO Tasks
- `GET /api/local-seo/profiles/{id}/tasks/` - List SEO tasks
- `PUT /api/local-seo/tasks/{id}/` - Update task status

### Dashboard
- `GET /api/local-seo/dashboard/` - Get dashboard overview

## 🔧 Usage Examples

### Creating a Business Profile

```python
import requests

data = {
    "business_name": "Sydney Coffee House",
    "address_line_1": "123 George Street",
    "city": "Sydney",
    "state": "NSW",
    "postal_code": "2000",
    "country": "Australia",
    "phone_number": "+61 2 9876 5432",
    "business_type": "restaurant"
}

response = requests.post(
    "http://localhost:8000/api/local-seo/profiles/",
    json=data,
    headers={"Authorization": "Bearer your_jwt_token"}
)
```

### Running an Audit

```python
business_profile_id = "your-business-profile-uuid"

response = requests.post(
    f"http://localhost:8000/api/local-seo/profiles/{business_profile_id}/audit/",
    headers={"Authorization": "Bearer your_jwt_token"}
)

audit_report = response.json()["audit_report"]
print(f"Overall Score: {audit_report['overall_score']}/100")
```

## 🧪 Testing

Run the test suite:

```bash
python manage.py test local_seo
```

## 📈 Monitoring & Analytics

### Key Metrics Tracked
- **Overall Local SEO Score**: Weighted composite score (0-100)
- **NAP Consistency Score**: Name, Address, Phone accuracy (0-100)
- **Profile Completeness Score**: Google Business Profile completeness (0-100)
- **Review Health Score**: Review volume, rating, and sentiment (0-100)
- **Local Visibility Score**: Keyword ranking performance (0-100)

### Audit Frequency
- **Real-time**: On-demand audits via API
- **Daily**: Google Business Profile synchronization
- **Weekly**: Keyword ranking updates
- **Monthly**: Comprehensive audit reports

## 🔐 Security & Permissions

### Role-Based Access Control
- **Admin**: Full access + analytics
- **Agency User**: Manage clients and export reports
- **Client (Business Owner)**: View-only access to own location dashboards

### API Authentication
- JWT token-based authentication
- User-scoped data access
- Rate limiting and throttling

## 🚀 Deployment

The Local SEO module is designed to integrate seamlessly with the existing SEOAnalyser infrastructure:

1. **Celery Workers**: Background task processing
2. **Redis**: Task queue and caching
3. **PostgreSQL**: Primary data storage
4. **Django Admin**: Administrative interface

## 📞 Support

For technical support or feature requests, please contact the development team or create an issue in the project repository.

---

**Built with ❤️ for Australian businesses by the SEOAnalyser.com.au team**
