import logging
from celery import shared_task
from django.utils import timezone
from django.contrib.auth import get_user_model
from .models import BusinessProfile, LocalKeyword, LocalAuditReport, Review
from .services.audit_service import LocalSEOAuditService
from .services.google_business_profile import GoogleBusinessProfileService
from .services.openai_service import OpenAILocalSEOService
from .services.serp_service import SERPTrackingService

User = get_user_model()
logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def run_local_seo_audit(self, business_profile_id):
    """
    Run a comprehensive Local SEO audit for a business profile
    
    Args:
        business_profile_id: UUID of the business profile to audit
    """
    try:
        business_profile = BusinessProfile.objects.get(id=business_profile_id)
        logger.info(f"Starting Local SEO audit task for {business_profile.business_name}")
        
        audit_service = LocalSEOAuditService()
        audit_report = audit_service.conduct_full_audit(business_profile)
        
        logger.info(f"Completed Local SEO audit for {business_profile.business_name}")
        return {
            'status': 'success',
            'audit_report_id': str(audit_report.id),
            'overall_score': audit_report.overall_score
        }
        
    except BusinessProfile.DoesNotExist:
        logger.error(f"Business profile {business_profile_id} not found")
        return {'status': 'error', 'message': 'Business profile not found'}
    except Exception as e:
        logger.error(f"Error running Local SEO audit for {business_profile_id}: {e}")
        # Retry the task
        raise self.retry(countdown=60, exc=e)


@shared_task(bind=True, max_retries=3)
def sync_google_business_profile(self, business_profile_id):
    """
    Sync business profile data with Google Business Profile
    
    Args:
        business_profile_id: UUID of the business profile to sync
    """
    try:
        business_profile = BusinessProfile.objects.get(id=business_profile_id)
        
        if not business_profile.google_place_id:
            logger.warning(f"No Google Place ID for {business_profile.business_name}")
            return {'status': 'skipped', 'message': 'No Google Place ID'}
        
        logger.info(f"Syncing Google Business Profile for {business_profile.business_name}")
        
        gbp_service = GoogleBusinessProfileService()
        gbp_data = gbp_service.get_business_details(business_profile.google_place_id)
        
        if gbp_data:
            # Update business profile with Google data
            business_profile.total_reviews = gbp_data.get('user_ratings_total', 0)
            business_profile.average_rating = gbp_data.get('rating', 0)
            business_profile.last_synced_at = timezone.now()
            business_profile.save()
            
            logger.info(f"Successfully synced Google Business Profile for {business_profile.business_name}")
            return {
                'status': 'success',
                'reviews': gbp_data.get('user_ratings_total', 0),
                'rating': gbp_data.get('rating', 0)
            }
        else:
            logger.warning(f"Could not fetch Google Business Profile data for {business_profile.business_name}")
            return {'status': 'error', 'message': 'Could not fetch Google data'}
            
    except BusinessProfile.DoesNotExist:
        logger.error(f"Business profile {business_profile_id} not found")
        return {'status': 'error', 'message': 'Business profile not found'}
    except Exception as e:
        logger.error(f"Error syncing Google Business Profile for {business_profile_id}: {e}")
        raise self.retry(countdown=60, exc=e)


@shared_task(bind=True, max_retries=3)
def track_keyword_rankings(self, keyword_id):
    """
    Track rankings for a specific keyword
    
    Args:
        keyword_id: UUID of the keyword to track
    """
    try:
        keyword = LocalKeyword.objects.get(id=keyword_id)
        logger.info(f"Tracking rankings for keyword: {keyword.keyword}")
        
        serp_service = SERPTrackingService()
        ranking_data = serp_service.track_local_keyword_rankings(
            keyword.keyword,
            keyword.target_location,
            keyword.business_profile.business_name,
            keyword.business_profile.google_place_id
        )
        
        # Save ranking data if successful
        if ranking_data.get('status') == 'success':
            from .models import KeywordRanking
            KeywordRanking.objects.create(
                keyword=keyword,
                ranking_type='local_pack' if ranking_data.get('is_in_local_pack') else 'organic',
                position=ranking_data.get('local_pack_position') or ranking_data.get('organic_position'),
                is_in_local_pack=ranking_data.get('is_in_local_pack', False),
                is_in_maps=ranking_data.get('is_in_maps', False),
                has_reviews=ranking_data.get('has_reviews', False),
                has_photos=ranking_data.get('has_photos', False),
                has_website_link=ranking_data.get('has_website_link', False)
            )
            
            logger.info(f"Successfully tracked rankings for keyword: {keyword.keyword}")
            return {
                'status': 'success',
                'keyword': keyword.keyword,
                'position': ranking_data.get('local_pack_position') or ranking_data.get('organic_position'),
                'in_local_pack': ranking_data.get('is_in_local_pack', False)
            }
        else:
            logger.warning(f"Failed to track rankings for keyword: {keyword.keyword}")
            return {'status': 'error', 'message': 'Failed to track rankings'}
            
    except LocalKeyword.DoesNotExist:
        logger.error(f"Keyword {keyword_id} not found")
        return {'status': 'error', 'message': 'Keyword not found'}
    except Exception as e:
        logger.error(f"Error tracking keyword rankings for {keyword_id}: {e}")
        raise self.retry(countdown=60, exc=e)


@shared_task(bind=True, max_retries=3)
def analyze_reviews_sentiment(self, business_profile_id):
    """
    Analyze sentiment for all reviews of a business profile
    
    Args:
        business_profile_id: UUID of the business profile
    """
    try:
        business_profile = BusinessProfile.objects.get(id=business_profile_id)
        logger.info(f"Analyzing review sentiment for {business_profile.business_name}")
        
        # Get reviews from database
        reviews = Review.objects.filter(
            business_profile=business_profile,
            sentiment__isnull=True  # Only analyze reviews without sentiment
        )[:20]  # Limit to 20 reviews per batch
        
        if not reviews:
            logger.info(f"No reviews to analyze for {business_profile.business_name}")
            return {'status': 'skipped', 'message': 'No reviews to analyze'}
        
        ai_service = OpenAILocalSEOService()
        
        # Analyze sentiment for each review
        analyzed_count = 0
        for review in reviews:
            if review.review_text:
                topics = ai_service.extract_review_topics(review.review_text)
                
                # Simple sentiment analysis based on rating
                if review.rating >= 4:
                    sentiment = 'positive'
                    sentiment_score = 0.5 + (review.rating - 4) * 0.5
                elif review.rating >= 3:
                    sentiment = 'neutral'
                    sentiment_score = 0.0
                else:
                    sentiment = 'negative'
                    sentiment_score = -0.5 - (3 - review.rating) * 0.25
                
                # Update review with sentiment data
                review.sentiment = sentiment
                review.sentiment_score = sentiment_score
                review.key_topics = topics
                review.save()
                
                analyzed_count += 1
        
        logger.info(f"Analyzed sentiment for {analyzed_count} reviews of {business_profile.business_name}")
        return {
            'status': 'success',
            'analyzed_count': analyzed_count,
            'business_name': business_profile.business_name
        }
        
    except BusinessProfile.DoesNotExist:
        logger.error(f"Business profile {business_profile_id} not found")
        return {'status': 'error', 'message': 'Business profile not found'}
    except Exception as e:
        logger.error(f"Error analyzing review sentiment for {business_profile_id}: {e}")
        raise self.retry(countdown=60, exc=e)


@shared_task
def daily_profile_sync():
    """
    Daily task to sync all active business profiles with Google Business Profile
    """
    logger.info("Starting daily profile sync task")
    
    active_profiles = BusinessProfile.objects.filter(
        status='active',
        google_place_id__isnull=False
    )
    
    sync_count = 0
    for profile in active_profiles:
        try:
            # Schedule individual sync tasks
            sync_google_business_profile.delay(str(profile.id))
            sync_count += 1
        except Exception as e:
            logger.error(f"Error scheduling sync for profile {profile.id}: {e}")
    
    logger.info(f"Scheduled sync for {sync_count} business profiles")
    return {'status': 'success', 'scheduled_syncs': sync_count}


@shared_task
def weekly_keyword_tracking():
    """
    Weekly task to track rankings for all active keywords
    """
    logger.info("Starting weekly keyword tracking task")
    
    active_keywords = LocalKeyword.objects.filter(
        is_active=True,
        business_profile__status='active'
    )
    
    tracking_count = 0
    for keyword in active_keywords:
        try:
            # Schedule individual tracking tasks
            track_keyword_rankings.delay(str(keyword.id))
            tracking_count += 1
        except Exception as e:
            logger.error(f"Error scheduling tracking for keyword {keyword.id}: {e}")
    
    logger.info(f"Scheduled tracking for {tracking_count} keywords")
    return {'status': 'success', 'scheduled_tracking': tracking_count}


@shared_task
def monthly_audit_reports():
    """
    Monthly task to generate audit reports for all active business profiles
    """
    logger.info("Starting monthly audit reports task")
    
    active_profiles = BusinessProfile.objects.filter(
        status='active'
    )
    
    audit_count = 0
    for profile in active_profiles:
        try:
            # Check if there's a recent audit (within last 30 days)
            recent_audit = profile.audit_reports.filter(
                audit_date__gte=timezone.now() - timezone.timedelta(days=30),
                status='completed'
            ).exists()
            
            if not recent_audit:
                # Schedule audit task
                run_local_seo_audit.delay(str(profile.id))
                audit_count += 1
        except Exception as e:
            logger.error(f"Error scheduling audit for profile {profile.id}: {e}")
    
    logger.info(f"Scheduled audits for {audit_count} business profiles")
    return {'status': 'success', 'scheduled_audits': audit_count}


@shared_task(bind=True, max_retries=3)
def update_competitor_data(self, business_profile_id):
    """
    Update competitor data for a business profile
    
    Args:
        business_profile_id: UUID of the business profile
    """
    try:
        business_profile = BusinessProfile.objects.get(id=business_profile_id)
        
        if not business_profile.google_place_id:
            logger.warning(f"No Google Place ID for {business_profile.business_name}")
            return {'status': 'skipped', 'message': 'No Google Place ID'}
        
        logger.info(f"Updating competitor data for {business_profile.business_name}")
        
        gbp_service = GoogleBusinessProfileService()
        competitors = gbp_service.find_competitors(business_profile.google_place_id)
        
        updated_count = 0
        for competitor_data in competitors:
            from .models import CompetitorProfile
            competitor, created = CompetitorProfile.objects.update_or_create(
                business_profile=business_profile,
                google_place_id=competitor_data.get('place_id'),
                defaults={
                    'competitor_name': competitor_data.get('name', ''),
                    'address': competitor_data.get('formatted_address', ''),
                    'phone_number': competitor_data.get('formatted_phone_number', ''),
                    'website_url': competitor_data.get('website', ''),
                    'primary_category': competitor_data.get('types', [None])[0],
                    'additional_categories': competitor_data.get('types', []),
                    'total_reviews': competitor_data.get('user_ratings_total', 0),
                    'average_rating': competitor_data.get('rating', 0),
                    'distance_km': competitor_data.get('distance_km', 0),
                    'last_analyzed_at': timezone.now()
                }
            )
            updated_count += 1
        
        logger.info(f"Updated {updated_count} competitors for {business_profile.business_name}")
        return {
            'status': 'success',
            'updated_competitors': updated_count,
            'business_name': business_profile.business_name
        }
        
    except BusinessProfile.DoesNotExist:
        logger.error(f"Business profile {business_profile_id} not found")
        return {'status': 'error', 'message': 'Business profile not found'}
    except Exception as e:
        logger.error(f"Error updating competitor data for {business_profile_id}: {e}")
        raise self.retry(countdown=60, exc=e)
