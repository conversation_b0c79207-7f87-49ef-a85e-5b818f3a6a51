from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from .models import BusinessProfile, LocalKeyword, LocalAuditReport
from .services.google_business_profile import GoogleBusinessProfileService
from .services.openai_service import OpenAILocalSEOService

User = get_user_model()


class BusinessProfileModelTest(TestCase):
    """Test BusinessProfile model"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_business_profile_creation(self):
        """Test creating a business profile"""
        profile = BusinessProfile.objects.create(
            user=self.user,
            business_name='Test Business',
            address_line_1='123 Test St',
            city='Sydney',
            state='NSW',
            postal_code='2000',
            country='Australia',
            phone_number='+61 2 1234 5678',
            business_type='restaurant'
        )
        
        self.assertEqual(profile.business_name, 'Test Business')
        self.assertEqual(profile.city, 'Sydney')
        self.assertEqual(profile.user, self.user)
        self.assertIn('Sydney', profile.full_address)
    
    def test_business_profile_str(self):
        """Test string representation"""
        profile = BusinessProfile.objects.create(
            user=self.user,
            business_name='Test Business',
            city='Sydney',
            state='NSW'
        )
        
        expected = 'Test Business - Sydney, NSW'
        self.assertEqual(str(profile), expected)


class LocalKeywordModelTest(TestCase):
    """Test LocalKeyword model"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.business_profile = BusinessProfile.objects.create(
            user=self.user,
            business_name='Test Business',
            city='Sydney',
            state='NSW'
        )
    
    def test_local_keyword_creation(self):
        """Test creating a local keyword"""
        keyword = LocalKeyword.objects.create(
            business_profile=self.business_profile,
            keyword='restaurant Sydney',
            target_location='Sydney, NSW, Australia',
            search_volume=1000,
            competition_level='medium'
        )
        
        self.assertEqual(keyword.keyword, 'restaurant Sydney')
        self.assertEqual(keyword.business_profile, self.business_profile)
        self.assertTrue(keyword.is_active)


class BusinessProfileAPITest(APITestCase):
    """Test BusinessProfile API endpoints"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
    
    def test_create_business_profile(self):
        """Test creating a business profile via API"""
        url = reverse('local_seo:business-profile-list')
        data = {
            'business_name': 'Test Restaurant',
            'address_line_1': '123 Test St',
            'city': 'Sydney',
            'state': 'NSW',
            'postal_code': '2000',
            'country': 'Australia',
            'phone_number': '+61 2 1234 5678',
            'business_type': 'restaurant'
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(BusinessProfile.objects.count(), 1)
        
        profile = BusinessProfile.objects.first()
        self.assertEqual(profile.business_name, 'Test Restaurant')
        self.assertEqual(profile.user, self.user)
    
    def test_list_business_profiles(self):
        """Test listing business profiles"""
        # Create a business profile
        BusinessProfile.objects.create(
            user=self.user,
            business_name='Test Business',
            city='Sydney',
            state='NSW'
        )
        
        url = reverse('local_seo:business-profile-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
    
    def test_unauthorized_access(self):
        """Test that unauthorized users cannot access profiles"""
        self.client.force_authenticate(user=None)
        
        url = reverse('local_seo:business-profile-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class GoogleBusinessProfileServiceTest(TestCase):
    """Test Google Business Profile service"""
    
    def setUp(self):
        self.service = GoogleBusinessProfileService()
    
    def test_service_initialization(self):
        """Test service initializes correctly"""
        self.assertIsNotNone(self.service.maps_client)
        self.assertIsNotNone(self.service.api_key)
    
    def test_extract_business_data_from_place(self):
        """Test extracting business data from place result"""
        place_data = {
            'place_id': 'test_place_id',
            'name': 'Test Business',
            'formatted_address': '123 Test St, Sydney NSW 2000, Australia',
            'rating': 4.5,
            'user_ratings_total': 100,
            'types': ['restaurant', 'food', 'establishment']
        }
        
        result = self.service._extract_business_data_from_place(place_data)
        
        self.assertEqual(result['place_id'], 'test_place_id')
        self.assertEqual(result['name'], 'Test Business')
        self.assertEqual(result['rating'], 4.5)
        self.assertEqual(result['user_ratings_total'], 100)


class OpenAILocalSEOServiceTest(TestCase):
    """Test OpenAI Local SEO service"""
    
    def setUp(self):
        self.service = OpenAILocalSEOService()
    
    def test_service_initialization(self):
        """Test service initializes correctly"""
        self.assertIsNotNone(self.service.client)
        self.assertIsNotNone(self.service.model)
    
    def test_fallback_sentiment_analysis(self):
        """Test fallback sentiment analysis"""
        reviews = [
            {'rating': 5, 'text': 'Great service!'},
            {'rating': 4, 'text': 'Good food'},
            {'rating': 3, 'text': 'Average experience'}
        ]
        
        result = self.service._fallback_sentiment_analysis(reviews)
        
        self.assertIn('overall_sentiment', result)
        self.assertIn('sentiment_score', result)
        self.assertIn('topics', result)
        self.assertIn('summary', result)
        self.assertEqual(result['overall_sentiment'], 'positive')  # Average rating is 4.0
    
    def test_fallback_recommendations(self):
        """Test fallback recommendations"""
        result = self.service._fallback_recommendations()
        
        self.assertIn('recommendations', result)
        self.assertIn('strategy_summary', result)
        self.assertIn('quick_wins', result)
        self.assertIsInstance(result['recommendations'], list)
        self.assertGreater(len(result['recommendations']), 0)


class DashboardAPITest(APITestCase):
    """Test dashboard API endpoint"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
        
        # Create test data
        self.business_profile = BusinessProfile.objects.create(
            user=self.user,
            business_name='Test Business',
            city='Sydney',
            state='NSW',
            total_reviews=50,
            average_rating=4.2
        )
    
    def test_dashboard_overview(self):
        """Test dashboard overview endpoint"""
        url = reverse('local_seo:dashboard-overview')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.data
        self.assertIn('total_profiles', data)
        self.assertIn('verified_profiles', data)
        self.assertIn('total_reviews', data)
        self.assertIn('average_rating', data)
        self.assertIn('pending_tasks', data)
        self.assertIn('recent_audits', data)
        
        self.assertEqual(data['total_profiles'], 1)
        self.assertEqual(data['total_reviews'], 50)
        self.assertEqual(float(data['average_rating']), 4.2)


class LocalAuditReportModelTest(TestCase):
    """Test LocalAuditReport model"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.business_profile = BusinessProfile.objects.create(
            user=self.user,
            business_name='Test Business',
            city='Sydney',
            state='NSW'
        )
    
    def test_audit_report_creation(self):
        """Test creating an audit report"""
        audit_report = LocalAuditReport.objects.create(
            business_profile=self.business_profile,
            overall_score=75,
            nap_consistency_score=80,
            profile_completeness_score=70,
            review_health_score=85,
            local_visibility_score=65,
            status='completed'
        )
        
        self.assertEqual(audit_report.business_profile, self.business_profile)
        self.assertEqual(audit_report.overall_score, 75)
        self.assertEqual(audit_report.status, 'completed')
    
    def test_audit_report_str(self):
        """Test string representation"""
        audit_report = LocalAuditReport.objects.create(
            business_profile=self.business_profile,
            status='completed'
        )
        
        expected = f"Audit Report - {self.business_profile.business_name} ({audit_report.audit_date.date()})"
        self.assertEqual(str(audit_report), expected)
