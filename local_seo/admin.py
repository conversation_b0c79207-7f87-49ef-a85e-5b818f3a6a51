from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    BusinessProfile, LocalKeyword, KeywordRanking, CompetitorProfile,
    Review, LocalAuditReport, SentimentTag, LocalSEOTask
)


@admin.register(BusinessProfile)
class BusinessProfileAdmin(admin.ModelAdmin):
    list_display = [
        'business_name', 'user', 'city', 'state', 'status', 
        'is_verified', 'total_reviews', 'average_rating', 
        'profile_completeness_score', 'created_at'
    ]
    list_filter = [
        'status', 'is_verified', 'business_type', 'state', 
        'created_at', 'last_synced_at'
    ]
    search_fields = [
        'business_name', 'user__email', 'city', 'state', 
        'phone_number', 'google_place_id'
    ]
    readonly_fields = [
        'id', 'created_at', 'updated_at', 'last_synced_at',
        'profile_completeness_score'
    ]
    fieldsets = (
        ('Basic Information', {
            'fields': (
                'id', 'user', 'business_name', 'business_type',
                'google_place_id', 'google_business_profile_url'
            )
        }),
        ('NAP Information', {
            'fields': (
                'address_line_1', 'address_line_2', 'city', 'state',
                'postal_code', 'country', 'phone_number', 'website_url'
            )
        }),
        ('Business Details', {
            'fields': (
                'primary_category', 'additional_categories', 'business_hours'
            )
        }),
        ('Metrics', {
            'fields': (
                'status', 'is_verified', 'total_reviews', 'average_rating',
                'profile_completeness_score'
            )
        }),
        ('Tracking', {
            'fields': ('created_at', 'updated_at', 'last_synced_at')
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(LocalKeyword)
class LocalKeywordAdmin(admin.ModelAdmin):
    list_display = [
        'keyword', 'business_profile', 'target_location', 'search_volume',
        'competition_level', 'intent', 'is_active', 'created_at'
    ]
    list_filter = [
        'intent', 'competition_level', 'is_active', 'created_at'
    ]
    search_fields = [
        'keyword', 'target_location', 'business_profile__business_name'
    ]
    readonly_fields = ['id', 'created_at', 'updated_at']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('business_profile')


@admin.register(KeywordRanking)
class KeywordRankingAdmin(admin.ModelAdmin):
    list_display = [
        'keyword', 'ranking_type', 'position', 'is_in_local_pack',
        'is_in_maps', 'check_date'
    ]
    list_filter = [
        'ranking_type', 'is_in_local_pack', 'is_in_maps',
        'has_reviews', 'has_photos', 'check_date'
    ]
    search_fields = [
        'keyword__keyword', 'keyword__business_profile__business_name'
    ]
    readonly_fields = ['id', 'created_at']
    date_hierarchy = 'check_date'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'keyword', 'keyword__business_profile'
        )


@admin.register(CompetitorProfile)
class CompetitorProfileAdmin(admin.ModelAdmin):
    list_display = [
        'competitor_name', 'business_profile', 'total_reviews',
        'average_rating', 'distance_km', 'competition_score',
        'is_active', 'last_analyzed_at'
    ]
    list_filter = [
        'is_active', 'primary_category', 'created_at', 'last_analyzed_at'
    ]
    search_fields = [
        'competitor_name', 'google_place_id', 
        'business_profile__business_name'
    ]
    readonly_fields = [
        'id', 'created_at', 'updated_at', 'last_analyzed_at'
    ]
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('business_profile')


@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = [
        'reviewer_name', 'business_profile', 'rating', 'sentiment',
        'review_date', 'is_flagged', 'has_response'
    ]
    list_filter = [
        'rating', 'sentiment', 'is_flagged', 'review_date'
    ]
    search_fields = [
        'reviewer_name', 'review_text', 'business_response',
        'business_profile__business_name', 'google_review_id'
    ]
    readonly_fields = [
        'id', 'google_review_id', 'sentiment_score', 'key_topics',
        'created_at', 'updated_at'
    ]
    date_hierarchy = 'review_date'
    
    def has_response(self, obj):
        return bool(obj.business_response)
    has_response.boolean = True
    has_response.short_description = 'Has Response'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('business_profile')


@admin.register(LocalAuditReport)
class LocalAuditReportAdmin(admin.ModelAdmin):
    list_display = [
        'business_profile', 'audit_date', 'status', 'overall_score',
        'nap_consistency_score', 'profile_completeness_score',
        'review_health_score', 'local_visibility_score'
    ]
    list_filter = [
        'status', 'audit_date', 'overall_score'
    ]
    search_fields = [
        'business_profile__business_name', 'ai_summary'
    ]
    readonly_fields = [
        'id', 'audit_results', 'recommendations', 'ai_summary',
        'priority_tasks', 'created_at', 'updated_at'
    ]
    date_hierarchy = 'audit_date'
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'business_profile', 'audit_date', 'status')
        }),
        ('Audit Scores', {
            'fields': (
                'overall_score', 'nap_consistency_score',
                'profile_completeness_score', 'review_health_score',
                'local_visibility_score'
            )
        }),
        ('Results & Recommendations', {
            'fields': ('audit_results', 'recommendations', 'ai_summary', 'priority_tasks'),
            'classes': ('collapse',)
        }),
        ('Tracking', {
            'fields': ('created_at', 'updated_at')
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('business_profile')


@admin.register(SentimentTag)
class SentimentTagAdmin(admin.ModelAdmin):
    list_display = [
        'tag_name', 'tag_type', 'business_profile', 'mention_count',
        'sentiment_score', 'first_mentioned', 'last_mentioned', 'is_active'
    ]
    list_filter = [
        'tag_type', 'is_active', 'first_mentioned', 'last_mentioned'
    ]
    search_fields = [
        'tag_name', 'description', 'business_profile__business_name'
    ]
    readonly_fields = [
        'id', 'mention_count', 'sentiment_score', 'first_mentioned',
        'last_mentioned', 'created_at', 'updated_at'
    ]
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('business_profile')


@admin.register(LocalSEOTask)
class LocalSEOTaskAdmin(admin.ModelAdmin):
    list_display = [
        'title', 'business_profile', 'category', 'priority', 'status',
        'estimated_impact', 'assigned_to', 'due_date', 'created_at'
    ]
    list_filter = [
        'priority', 'status', 'category', 'due_date', 'created_at'
    ]
    search_fields = [
        'title', 'description', 'business_profile__business_name',
        'assigned_to__email'
    ]
    readonly_fields = [
        'id', 'created_at', 'updated_at', 'completed_at'
    ]
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('Basic Information', {
            'fields': (
                'id', 'business_profile', 'audit_report', 'title',
                'description', 'category'
            )
        }),
        ('Task Details', {
            'fields': (
                'priority', 'status', 'estimated_impact', 'estimated_effort'
            )
        }),
        ('Assignment & Tracking', {
            'fields': (
                'assigned_to', 'due_date', 'completed_at'
            )
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'business_profile', 'assigned_to', 'audit_report'
        )
