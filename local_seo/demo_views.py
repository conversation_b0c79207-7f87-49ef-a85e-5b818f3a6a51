from django.shortcuts import render
from django.http import HttpResponse
from django.views.generic import TemplateView
from django.conf import settings
import os


class LocalSEODemoView(TemplateView):
    """Serve the Local SEO demo HTML page"""
    
    def get(self, request, *args, **kwargs):
        # Read the demo HTML file
        demo_path = os.path.join(settings.BASE_DIR, 'static', 'local_seo_demo', 'index.html')
        
        try:
            with open(demo_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            return HttpResponse(html_content, content_type='text/html')
        except FileNotFoundError:
            return HttpResponse(
                '<h1>Local SEO Demo Not Found</h1>'
                '<p>Please ensure the demo files are in static/local_seo_demo/</p>',
                status=404
            )


def demo_app_js(request):
    """Serve the demo JavaScript file"""
    js_path = os.path.join(settings.BASE_DIR, 'static', 'local_seo_demo', 'app.js')
    
    try:
        with open(js_path, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        return HttpResponse(js_content, content_type='application/javascript')
    except FileNotFoundError:
        return HttpResponse('// Demo JS not found', content_type='application/javascript', status=404)
