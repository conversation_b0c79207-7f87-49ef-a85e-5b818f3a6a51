import uuid
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from django.contrib.postgres.fields import ArrayField

User = get_user_model()


class BusinessProfile(models.Model):
    """Model to store Google Business Profile information"""

    STATUS_CHOICES = [
        ('active', _('Active')),
        ('inactive', _('Inactive')),
        ('suspended', _('Suspended')),
        ('pending', _('Pending Verification')),
    ]

    BUSINESS_TYPE_CHOICES = [
        ('restaurant', _('Restaurant')),
        ('retail', _('Retail Store')),
        ('service', _('Service Business')),
        ('healthcare', _('Healthcare')),
        ('automotive', _('Automotive')),
        ('beauty', _('Beauty & Spa')),
        ('professional', _('Professional Services')),
        ('real_estate', _('Real Estate')),
        ('education', _('Education')),
        ('entertainment', _('Entertainment')),
        ('other', _('Other')),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='business_profiles')

    # Basic Business Information
    business_name = models.CharField(_('Business Name'), max_length=255)
    google_place_id = models.CharField(_('Google Place ID'), max_length=255, unique=True, null=True, blank=True)
    google_business_profile_url = models.URLField(_('Google Business Profile URL'), null=True, blank=True)

    # NAP (Name, Address, Phone) Information
    address_line_1 = models.CharField(_('Address Line 1'), max_length=255)
    address_line_2 = models.CharField(_('Address Line 2'), max_length=255, blank=True)
    city = models.CharField(_('City'), max_length=100)
    state = models.CharField(_('State/Province'), max_length=100)
    postal_code = models.CharField(_('Postal Code'), max_length=20)
    country = models.CharField(_('Country'), max_length=100, default='Australia')
    phone_number = models.CharField(_('Phone Number'), max_length=20)
    website_url = models.URLField(_('Website URL'), null=True, blank=True)

    # Business Details
    business_type = models.CharField(_('Business Type'), max_length=50, choices=BUSINESS_TYPE_CHOICES)
    primary_category = models.CharField(_('Primary Category'), max_length=255, null=True, blank=True)
    additional_categories = ArrayField(
        models.CharField(max_length=255),
        size=10,
        default=list,
        blank=True,
        help_text=_('Additional business categories')
    )

    # Business Hours (stored as JSON)
    business_hours = models.JSONField(_('Business Hours'), default=dict, blank=True)

    # Profile Status and Metrics
    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='active')
    is_verified = models.BooleanField(_('Is Verified'), default=False)
    total_reviews = models.PositiveIntegerField(_('Total Reviews'), default=0)
    average_rating = models.DecimalField(_('Average Rating'), max_digits=3, decimal_places=2, default=0.00)

    # Profile Completeness
    profile_completeness_score = models.PositiveIntegerField(
        _('Profile Completeness Score'),
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )

    # Tracking
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)
    last_synced_at = models.DateTimeField(_('Last Synced At'), null=True, blank=True)

    class Meta:
        verbose_name = _('Business Profile')
        verbose_name_plural = _('Business Profiles')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['google_place_id']),
            models.Index(fields=['city', 'state']),
        ]

    def __str__(self):
        return f"{self.business_name} - {self.city}, {self.state}"

    @property
    def full_address(self):
        """Return the full formatted address"""
        address_parts = [self.address_line_1]
        if self.address_line_2:
            address_parts.append(self.address_line_2)
        address_parts.extend([self.city, self.state, self.postal_code, self.country])
        return ', '.join(address_parts)


class LocalKeyword(models.Model):
    """Model to store local keywords for tracking"""

    INTENT_CHOICES = [
        ('informational', _('Informational')),
        ('navigational', _('Navigational')),
        ('transactional', _('Transactional')),
        ('local', _('Local')),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    business_profile = models.ForeignKey(BusinessProfile, on_delete=models.CASCADE, related_name='keywords')

    keyword = models.CharField(_('Keyword'), max_length=255)
    search_volume = models.PositiveIntegerField(_('Monthly Search Volume'), default=0)
    competition_level = models.CharField(_('Competition Level'), max_length=20, default='medium')
    intent = models.CharField(_('Search Intent'), max_length=20, choices=INTENT_CHOICES, default='local')

    # Geo-targeting
    target_location = models.CharField(_('Target Location'), max_length=255)
    radius_km = models.PositiveIntegerField(_('Radius (KM)'), default=10)

    # Tracking settings
    is_active = models.BooleanField(_('Is Active'), default=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Local Keyword')
        verbose_name_plural = _('Local Keywords')
        unique_together = ['business_profile', 'keyword', 'target_location']
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['business_profile', 'is_active']),
            models.Index(fields=['keyword']),
        ]

    def __str__(self):
        return f"{self.keyword} - {self.target_location}"


class KeywordRanking(models.Model):
    """Model to store keyword ranking history"""

    RANKING_TYPE_CHOICES = [
        ('local_pack', _('Local Pack')),
        ('organic', _('Organic')),
        ('maps', _('Google Maps')),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    keyword = models.ForeignKey(LocalKeyword, on_delete=models.CASCADE, related_name='rankings')

    ranking_type = models.CharField(_('Ranking Type'), max_length=20, choices=RANKING_TYPE_CHOICES)
    position = models.PositiveIntegerField(_('Position'), null=True, blank=True)
    is_in_local_pack = models.BooleanField(_('In Local Pack'), default=False)
    is_in_maps = models.BooleanField(_('In Google Maps'), default=False)

    # SERP Features
    has_reviews = models.BooleanField(_('Has Reviews'), default=False)
    has_photos = models.BooleanField(_('Has Photos'), default=False)
    has_website_link = models.BooleanField(_('Has Website Link'), default=False)

    # Tracking
    check_date = models.DateTimeField(_('Check Date'), default=timezone.now)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)

    class Meta:
        verbose_name = _('Keyword Ranking')
        verbose_name_plural = _('Keyword Rankings')
        ordering = ['-check_date']
        indexes = [
            models.Index(fields=['keyword', 'check_date']),
            models.Index(fields=['ranking_type', 'position']),
        ]

    def __str__(self):
        return f"{self.keyword.keyword} - Position {self.position} ({self.ranking_type})"


class CompetitorProfile(models.Model):
    """Model to store competitor business information"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    business_profile = models.ForeignKey(BusinessProfile, on_delete=models.CASCADE, related_name='competitors')

    # Competitor Information
    competitor_name = models.CharField(_('Competitor Name'), max_length=255)
    google_place_id = models.CharField(_('Google Place ID'), max_length=255, unique=True)
    google_business_profile_url = models.URLField(_('Google Business Profile URL'), null=True, blank=True)

    # NAP Information
    address = models.TextField(_('Address'))
    phone_number = models.CharField(_('Phone Number'), max_length=20, blank=True)
    website_url = models.URLField(_('Website URL'), null=True, blank=True)

    # Business Details
    primary_category = models.CharField(_('Primary Category'), max_length=255, null=True, blank=True)
    additional_categories = ArrayField(
        models.CharField(max_length=255),
        size=10,
        default=list,
        blank=True
    )

    # Metrics
    total_reviews = models.PositiveIntegerField(_('Total Reviews'), default=0)
    average_rating = models.DecimalField(_('Average Rating'), max_digits=3, decimal_places=2, default=0.00)

    # Distance and Competition
    distance_km = models.DecimalField(_('Distance (KM)'), max_digits=5, decimal_places=2, default=0.00)
    competition_score = models.PositiveIntegerField(
        _('Competition Score'),
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )

    # Tracking
    is_active = models.BooleanField(_('Is Active'), default=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)
    last_analyzed_at = models.DateTimeField(_('Last Analyzed At'), null=True, blank=True)

    class Meta:
        verbose_name = _('Competitor Profile')
        verbose_name_plural = _('Competitor Profiles')
        unique_together = ['business_profile', 'google_place_id']
        ordering = ['-competition_score', '-total_reviews']
        indexes = [
            models.Index(fields=['business_profile', 'is_active']),
            models.Index(fields=['google_place_id']),
        ]

    def __str__(self):
        return f"{self.competitor_name} (Competitor of {self.business_profile.business_name})"


class Review(models.Model):
    """Model to store Google Business Profile reviews"""

    SENTIMENT_CHOICES = [
        ('positive', _('Positive')),
        ('neutral', _('Neutral')),
        ('negative', _('Negative')),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    business_profile = models.ForeignKey(BusinessProfile, on_delete=models.CASCADE, related_name='reviews')

    # Review Information
    google_review_id = models.CharField(_('Google Review ID'), max_length=255, unique=True)
    reviewer_name = models.CharField(_('Reviewer Name'), max_length=255)
    reviewer_photo_url = models.URLField(_('Reviewer Photo URL'), null=True, blank=True)

    # Review Content
    rating = models.PositiveIntegerField(
        _('Rating'),
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    review_text = models.TextField(_('Review Text'), blank=True)
    review_date = models.DateTimeField(_('Review Date'))

    # Business Response
    business_response = models.TextField(_('Business Response'), blank=True)
    response_date = models.DateTimeField(_('Response Date'), null=True, blank=True)

    # AI Analysis
    sentiment = models.CharField(_('Sentiment'), max_length=20, choices=SENTIMENT_CHOICES, null=True, blank=True)
    sentiment_score = models.DecimalField(
        _('Sentiment Score'),
        max_digits=4,
        decimal_places=3,
        null=True,
        blank=True,
        help_text=_('AI-generated sentiment score (-1 to 1)')
    )
    key_topics = ArrayField(
        models.CharField(max_length=100),
        size=10,
        default=list,
        blank=True,
        help_text=_('AI-extracted key topics from review')
    )

    # Tracking
    is_flagged = models.BooleanField(_('Is Flagged'), default=False)
    flagged_reason = models.CharField(_('Flagged Reason'), max_length=255, blank=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Review')
        verbose_name_plural = _('Reviews')
        ordering = ['-review_date']
        indexes = [
            models.Index(fields=['business_profile', 'review_date']),
            models.Index(fields=['rating', 'sentiment']),
            models.Index(fields=['google_review_id']),
        ]

    def __str__(self):
        return f"{self.reviewer_name} - {self.rating}★ ({self.business_profile.business_name})"


class LocalAuditReport(models.Model):
    """Model to store comprehensive local SEO audit reports"""

    AUDIT_STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('in_progress', _('In Progress')),
        ('completed', _('Completed')),
        ('failed', _('Failed')),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    business_profile = models.ForeignKey(BusinessProfile, on_delete=models.CASCADE, related_name='audit_reports')

    # Audit Information
    audit_date = models.DateTimeField(_('Audit Date'), default=timezone.now)
    status = models.CharField(_('Status'), max_length=20, choices=AUDIT_STATUS_CHOICES, default='pending')

    # Audit Scores
    overall_score = models.PositiveIntegerField(
        _('Overall Score'),
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    nap_consistency_score = models.PositiveIntegerField(
        _('NAP Consistency Score'),
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    profile_completeness_score = models.PositiveIntegerField(
        _('Profile Completeness Score'),
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    review_health_score = models.PositiveIntegerField(
        _('Review Health Score'),
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    local_visibility_score = models.PositiveIntegerField(
        _('Local Visibility Score'),
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )

    # Detailed Audit Results (JSON)
    audit_results = models.JSONField(_('Audit Results'), default=dict)
    recommendations = models.JSONField(_('Recommendations'), default=list)

    # AI-Generated Summary
    ai_summary = models.TextField(_('AI Summary'), blank=True)
    priority_tasks = models.JSONField(_('Priority Tasks'), default=list)

    # Tracking
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Local Audit Report')
        verbose_name_plural = _('Local Audit Reports')
        ordering = ['-audit_date']
        indexes = [
            models.Index(fields=['business_profile', 'audit_date']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"Audit Report - {self.business_profile.business_name} ({self.audit_date.date()})"


class SentimentTag(models.Model):
    """Model to store AI-generated sentiment tags and topics"""

    TAG_TYPE_CHOICES = [
        ('topic', _('Topic')),
        ('emotion', _('Emotion')),
        ('issue', _('Issue')),
        ('praise', _('Praise')),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    business_profile = models.ForeignKey(BusinessProfile, on_delete=models.CASCADE, related_name='sentiment_tags')

    # Tag Information
    tag_name = models.CharField(_('Tag Name'), max_length=100)
    tag_type = models.CharField(_('Tag Type'), max_length=20, choices=TAG_TYPE_CHOICES)
    description = models.TextField(_('Description'), blank=True)

    # Metrics
    mention_count = models.PositiveIntegerField(_('Mention Count'), default=0)
    sentiment_score = models.DecimalField(
        _('Average Sentiment Score'),
        max_digits=4,
        decimal_places=3,
        default=0.000
    )

    # Tracking
    first_mentioned = models.DateTimeField(_('First Mentioned'), null=True, blank=True)
    last_mentioned = models.DateTimeField(_('Last Mentioned'), null=True, blank=True)
    is_active = models.BooleanField(_('Is Active'), default=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Sentiment Tag')
        verbose_name_plural = _('Sentiment Tags')
        unique_together = ['business_profile', 'tag_name', 'tag_type']
        ordering = ['-mention_count', '-last_mentioned']
        indexes = [
            models.Index(fields=['business_profile', 'tag_type']),
            models.Index(fields=['tag_name']),
        ]

    def __str__(self):
        return f"{self.tag_name} ({self.tag_type}) - {self.business_profile.business_name}"


class LocalSEOTask(models.Model):
    """Model to track local SEO improvement tasks"""

    PRIORITY_CHOICES = [
        ('low', _('Low')),
        ('medium', _('Medium')),
        ('high', _('High')),
        ('critical', _('Critical')),
    ]

    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('in_progress', _('In Progress')),
        ('completed', _('Completed')),
        ('ignored', _('Ignored')),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    business_profile = models.ForeignKey(BusinessProfile, on_delete=models.CASCADE, related_name='seo_tasks')
    audit_report = models.ForeignKey(
        LocalAuditReport,
        on_delete=models.CASCADE,
        related_name='tasks',
        null=True,
        blank=True
    )

    # Task Information
    title = models.CharField(_('Task Title'), max_length=255)
    description = models.TextField(_('Description'))
    category = models.CharField(_('Category'), max_length=100)
    priority = models.CharField(_('Priority'), max_length=20, choices=PRIORITY_CHOICES, default='medium')
    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='pending')

    # Task Details
    estimated_impact = models.PositiveIntegerField(
        _('Estimated Impact'),
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text=_('Estimated impact on local SEO score (0-100)')
    )
    estimated_effort = models.CharField(_('Estimated Effort'), max_length=50, blank=True)

    # Tracking
    assigned_to = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_seo_tasks'
    )
    due_date = models.DateTimeField(_('Due Date'), null=True, blank=True)
    completed_at = models.DateTimeField(_('Completed At'), null=True, blank=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Local SEO Task')
        verbose_name_plural = _('Local SEO Tasks')
        ordering = ['-priority', '-created_at']
        indexes = [
            models.Index(fields=['business_profile', 'status']),
            models.Index(fields=['priority', 'status']),
            models.Index(fields=['assigned_to', 'status']),
        ]

    def __str__(self):
        return f"{self.title} - {self.business_profile.business_name} ({self.priority})"
