import logging
from django.shortcuts import get_object_or_404
from django.utils import timezone
from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.db.models import Q, Count, Avg
from django.contrib.auth import get_user_model

from .models import (
    BusinessProfile, LocalKeyword, KeywordRanking, CompetitorProfile,
    Review, LocalAuditReport, SentimentTag, LocalSEOTask
)
from .serializers import (
    BusinessProfileSerializer, BusinessProfileCreateSerializer,
    BusinessProfileSummarySerializer, LocalKeywordSerializer,
    KeywordRankingSerializer, CompetitorProfileSerializer,
    ReviewSerializer, LocalAuditReportSerializer,
    LocalAuditReportDetailSerializer, SentimentTagSerializer,
    LocalSEOTaskSerializer, KeywordRankingTrendSerializer,
    CompetitorAnalysisSerializer
)
from .services.audit_service import LocalSEOAuditService
from .services.google_business_profile import GoogleBusinessProfileService
from .services.serp_service import SERPTrackingService

User = get_user_model()
logger = logging.getLogger(__name__)


class BusinessProfileListCreateView(generics.ListCreateAPIView):
    """List and create business profiles"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return BusinessProfileCreateSerializer
        return BusinessProfileSummarySerializer
    
    def get_queryset(self):
        return BusinessProfile.objects.filter(user=self.request.user).order_by('-created_at')


class BusinessProfileDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete a business profile"""
    
    serializer_class = BusinessProfileSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return BusinessProfile.objects.filter(user=self.request.user)


class BusinessProfileSearchView(APIView):
    """Search for businesses using Google Places API"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        business_name = request.data.get('business_name')
        location = request.data.get('location')
        
        if not business_name or not location:
            return Response(
                {'error': 'business_name and location are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            gbp_service = GoogleBusinessProfileService()
            businesses = gbp_service.search_business_by_name_and_location(
                business_name, location
            )
            
            return Response({
                'businesses': businesses,
                'count': len(businesses)
            })
            
        except Exception as e:
            logger.error(f"Error searching for businesses: {e}")
            return Response(
                {'error': 'Failed to search for businesses'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class LocalAuditCreateView(APIView):
    """Create and run a new local SEO audit"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, business_profile_id):
        try:
            business_profile = get_object_or_404(
                BusinessProfile,
                id=business_profile_id,
                user=request.user
            )
            
            # Check if there's a recent audit (within last 24 hours)
            recent_audit = business_profile.audit_reports.filter(
                audit_date__gte=timezone.now() - timezone.timedelta(hours=24),
                status='completed'
            ).first()
            
            if recent_audit:
                return Response({
                    'message': 'Recent audit found',
                    'audit_report': LocalAuditReportSerializer(recent_audit).data
                })
            
            # Run new audit
            audit_service = LocalSEOAuditService()
            audit_report = audit_service.conduct_full_audit(business_profile)
            
            return Response({
                'message': 'Audit completed successfully',
                'audit_report': LocalAuditReportDetailSerializer(audit_report).data
            })
            
        except Exception as e:
            logger.error(f"Error running audit for business {business_profile_id}: {e}")
            return Response(
                {'error': 'Failed to run audit'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class LocalAuditReportListView(generics.ListAPIView):
    """List audit reports for a business profile"""
    
    serializer_class = LocalAuditReportSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        business_profile_id = self.kwargs['business_profile_id']
        return LocalAuditReport.objects.filter(
            business_profile_id=business_profile_id,
            business_profile__user=self.request.user
        ).order_by('-audit_date')


class LocalAuditReportDetailView(generics.RetrieveAPIView):
    """Retrieve detailed audit report"""
    
    serializer_class = LocalAuditReportDetailSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return LocalAuditReport.objects.filter(
            business_profile__user=self.request.user
        )


class LocalKeywordListCreateView(generics.ListCreateAPIView):
    """List and create local keywords for a business profile"""
    
    serializer_class = LocalKeywordSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        business_profile_id = self.kwargs['business_profile_id']
        return LocalKeyword.objects.filter(
            business_profile_id=business_profile_id,
            business_profile__user=self.request.user
        ).order_by('-created_at')
    
    def perform_create(self, serializer):
        business_profile_id = self.kwargs['business_profile_id']
        business_profile = get_object_or_404(
            BusinessProfile,
            id=business_profile_id,
            user=self.request.user
        )
        serializer.save(business_profile=business_profile)


class KeywordRankingTrackingView(APIView):
    """Track keyword rankings for a business"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, keyword_id):
        try:
            keyword = get_object_or_404(
                LocalKeyword,
                id=keyword_id,
                business_profile__user=request.user
            )
            
            serp_service = SERPTrackingService()
            ranking_data = serp_service.track_local_keyword_rankings(
                keyword.keyword,
                keyword.target_location,
                keyword.business_profile.business_name,
                keyword.business_profile.google_place_id
            )
            
            # Save ranking data
            if ranking_data.get('status') == 'success':
                KeywordRanking.objects.create(
                    keyword=keyword,
                    ranking_type='local_pack' if ranking_data.get('is_in_local_pack') else 'organic',
                    position=ranking_data.get('local_pack_position') or ranking_data.get('organic_position'),
                    is_in_local_pack=ranking_data.get('is_in_local_pack', False),
                    is_in_maps=ranking_data.get('is_in_maps', False)
                )
            
            return Response(ranking_data)
            
        except Exception as e:
            logger.error(f"Error tracking keyword rankings: {e}")
            return Response(
                {'error': 'Failed to track keyword rankings'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class KeywordRankingHistoryView(generics.ListAPIView):
    """Get keyword ranking history"""
    
    serializer_class = KeywordRankingSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        keyword_id = self.kwargs['keyword_id']
        return KeywordRanking.objects.filter(
            keyword_id=keyword_id,
            keyword__business_profile__user=self.request.user
        ).order_by('-check_date')


class CompetitorAnalysisView(APIView):
    """Analyze competitors for a business profile"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, business_profile_id):
        try:
            business_profile = get_object_or_404(
                BusinessProfile,
                id=business_profile_id,
                user=request.user
            )
            
            if not business_profile.google_place_id:
                return Response(
                    {'error': 'Google Place ID required for competitor analysis'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            gbp_service = GoogleBusinessProfileService()
            competitors = gbp_service.find_competitors(business_profile.google_place_id)
            
            # Save competitor data
            for competitor_data in competitors:
                CompetitorProfile.objects.update_or_create(
                    business_profile=business_profile,
                    google_place_id=competitor_data.get('place_id'),
                    defaults={
                        'competitor_name': competitor_data.get('name', ''),
                        'address': competitor_data.get('formatted_address', ''),
                        'phone_number': competitor_data.get('formatted_phone_number', ''),
                        'website_url': competitor_data.get('website', ''),
                        'primary_category': competitor_data.get('types', [None])[0],
                        'additional_categories': competitor_data.get('types', []),
                        'total_reviews': competitor_data.get('user_ratings_total', 0),
                        'average_rating': competitor_data.get('rating', 0),
                        'distance_km': competitor_data.get('distance_km', 0),
                        'last_analyzed_at': timezone.now()
                    }
                )
            
            # Get saved competitors
            saved_competitors = CompetitorProfile.objects.filter(
                business_profile=business_profile,
                is_active=True
            )
            
            # Calculate competitive metrics
            competitive_metrics = {
                'total_competitors': saved_competitors.count(),
                'avg_competitor_rating': saved_competitors.aggregate(
                    avg_rating=Avg('average_rating')
                )['avg_rating'] or 0,
                'avg_competitor_reviews': saved_competitors.aggregate(
                    avg_reviews=Avg('total_reviews')
                )['avg_reviews'] or 0,
            }
            
            return Response({
                'business_profile': BusinessProfileSummarySerializer(business_profile).data,
                'competitors': CompetitorProfileSerializer(saved_competitors, many=True).data,
                'competitive_metrics': competitive_metrics,
                'recommendations': self._generate_competitor_recommendations(
                    business_profile, competitive_metrics
                )
            })
            
        except Exception as e:
            logger.error(f"Error analyzing competitors: {e}")
            return Response(
                {'error': 'Failed to analyze competitors'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _generate_competitor_recommendations(self, business_profile, metrics):
        """Generate recommendations based on competitor analysis"""
        recommendations = []
        
        if business_profile.average_rating < metrics['avg_competitor_rating']:
            recommendations.append(
                "Your average rating is below competitors. Focus on improving customer satisfaction."
            )
        
        if business_profile.total_reviews < metrics['avg_competitor_reviews']:
            recommendations.append(
                "You have fewer reviews than competitors. Implement a review generation strategy."
            )
        
        return recommendations


class LocalSEOTaskListView(generics.ListAPIView):
    """List SEO tasks for a business profile"""
    
    serializer_class = LocalSEOTaskSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        business_profile_id = self.kwargs['business_profile_id']
        queryset = LocalSEOTask.objects.filter(
            business_profile_id=business_profile_id,
            business_profile__user=self.request.user
        )
        
        # Filter by status if provided
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Filter by priority if provided
        priority_filter = self.request.query_params.get('priority')
        if priority_filter:
            queryset = queryset.filter(priority=priority_filter)
        
        return queryset.order_by('-priority', '-created_at')


class LocalSEOTaskUpdateView(generics.UpdateAPIView):
    """Update SEO task status"""
    
    serializer_class = LocalSEOTaskSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return LocalSEOTask.objects.filter(
            business_profile__user=self.request.user
        )
    
    def perform_update(self, serializer):
        # Set completed_at when status changes to completed
        if serializer.validated_data.get('status') == 'completed':
            serializer.save(completed_at=timezone.now())
        else:
            serializer.save()


class ReviewAnalysisView(APIView):
    """Analyze reviews for a business profile"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, business_profile_id):
        try:
            business_profile = get_object_or_404(
                BusinessProfile,
                id=business_profile_id,
                user=request.user
            )
            
            if not business_profile.google_place_id:
                return Response(
                    {'error': 'Google Place ID required for review analysis'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            gbp_service = GoogleBusinessProfileService()
            reviews = gbp_service.get_business_reviews(business_profile.google_place_id)
            
            if not reviews:
                return Response({
                    'message': 'No reviews found for analysis',
                    'reviews': [],
                    'sentiment_analysis': {}
                })
            
            # Save reviews to database
            for review_data in reviews:
                Review.objects.update_or_create(
                    business_profile=business_profile,
                    google_review_id=review_data.get('author_name', '') + str(review_data.get('time', '')),
                    defaults={
                        'reviewer_name': review_data.get('author_name', ''),
                        'reviewer_photo_url': review_data.get('profile_photo_url', ''),
                        'rating': review_data.get('rating', 0),
                        'review_text': review_data.get('text', ''),
                        'review_date': timezone.datetime.fromtimestamp(
                            review_data.get('time', 0), tz=timezone.utc
                        ) if review_data.get('time') else timezone.now()
                    }
                )
            
            # Get saved reviews
            saved_reviews = Review.objects.filter(
                business_profile=business_profile
            ).order_by('-review_date')
            
            return Response({
                'reviews': ReviewSerializer(saved_reviews, many=True).data,
                'total_reviews': saved_reviews.count(),
                'average_rating': saved_reviews.aggregate(
                    avg_rating=Avg('rating')
                )['avg_rating'] or 0
            })
            
        except Exception as e:
            logger.error(f"Error analyzing reviews: {e}")
            return Response(
                {'error': 'Failed to analyze reviews'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def dashboard_overview(request):
    """Get dashboard overview data for the user"""
    try:
        user_profiles = BusinessProfile.objects.filter(user=request.user)
        
        overview_data = {
            'total_profiles': user_profiles.count(),
            'verified_profiles': user_profiles.filter(is_verified=True).count(),
            'total_reviews': sum(profile.total_reviews for profile in user_profiles),
            'average_rating': user_profiles.aggregate(
                avg_rating=Avg('average_rating')
            )['avg_rating'] or 0,
            'pending_tasks': LocalSEOTask.objects.filter(
                business_profile__user=request.user,
                status='pending'
            ).count(),
            'recent_audits': LocalAuditReportSerializer(
                LocalAuditReport.objects.filter(
                    business_profile__user=request.user,
                    status='completed'
                ).order_by('-audit_date')[:5],
                many=True
            ).data
        }
        
        return Response(overview_data)
        
    except Exception as e:
        logger.error(f"Error getting dashboard overview: {e}")
        return Response(
            {'error': 'Failed to get dashboard overview'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
