import logging
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.utils import timezone
from .models import BusinessProfile, LocalKeyword, Review
from .tasks import (
    sync_google_business_profile, track_keyword_rankings,
    analyze_reviews_sentiment, update_competitor_data
)

logger = logging.getLogger(__name__)


@receiver(post_save, sender=BusinessProfile)
def business_profile_post_save(sender, instance, created, **kwargs):
    """
    Handle post-save actions for BusinessProfile
    """
    if created:
        logger.info(f"New business profile created: {instance.business_name}")
        
        # Schedule initial sync if Google Place ID is available
        if instance.google_place_id:
            sync_google_business_profile.delay(str(instance.id))
            
            # Schedule competitor analysis
            update_competitor_data.delay(str(instance.id))
    else:
        # If Google Place ID was added, schedule sync
        if instance.google_place_id and instance.last_synced_at is None:
            sync_google_business_profile.delay(str(instance.id))
            update_competitor_data.delay(str(instance.id))


@receiver(post_save, sender=LocalKeyword)
def local_keyword_post_save(sender, instance, created, **kwargs):
    """
    Handle post-save actions for LocalKeyword
    """
    if created and instance.is_active:
        logger.info(f"New keyword created: {instance.keyword} for {instance.business_profile.business_name}")
        
        # Schedule initial ranking check
        track_keyword_rankings.delay(str(instance.id))


@receiver(post_save, sender=Review)
def review_post_save(sender, instance, created, **kwargs):
    """
    Handle post-save actions for Review
    """
    if created:
        logger.info(f"New review added for {instance.business_profile.business_name}")
        
        # Schedule sentiment analysis if review text exists and sentiment is not set
        if instance.review_text and not instance.sentiment:
            analyze_reviews_sentiment.delay(str(instance.business_profile.id))


# You can add more signal handlers here for other models as needed
