import logging
from typing import Dict, List, Optional, Any
from django.utils import timezone
from ..models import BusinessProfile, LocalAuditReport, LocalSEOTask
from .google_business_profile import GoogleBusinessProfileService
from .openai_service import OpenAILocalSEOService
from .serp_service import SERPTrackingService

logger = logging.getLogger(__name__)


class LocalSEOAuditService:
    """Main service for conducting comprehensive Local SEO audits"""
    
    def __init__(self):
        self.gbp_service = GoogleBusinessProfileService()
        self.ai_service = OpenAILocalSEOService()
        self.serp_service = SERPTrackingService()
    
    def conduct_full_audit(self, business_profile: BusinessProfile) -> LocalAuditReport:
        """
        Conduct a comprehensive Local SEO audit for a business profile
        
        Args:
            business_profile: BusinessProfile instance to audit
            
        Returns:
            LocalAuditReport instance with complete audit results
        """
        logger.info(f"Starting full Local SEO audit for {business_profile.business_name}")
        
        # Create audit report
        audit_report = LocalAuditReport.objects.create(
            business_profile=business_profile,
            status='in_progress'
        )
        
        try:
            # 1. Google Business Profile Audit
            gbp_audit = self._audit_google_business_profile(business_profile)
            
            # 2. NAP Consistency Check
            nap_audit = self._audit_nap_consistency(business_profile)
            
            # 3. Review Analysis
            review_audit = self._audit_reviews(business_profile)
            
            # 4. Local Visibility Check
            visibility_audit = self._audit_local_visibility(business_profile)
            
            # 5. Competitor Analysis
            competitor_audit = self._audit_competitors(business_profile)
            
            # Compile all audit results
            audit_results = {
                'google_business_profile': gbp_audit,
                'nap_consistency': nap_audit,
                'reviews': review_audit,
                'local_visibility': visibility_audit,
                'competitors': competitor_audit,
                'audit_timestamp': timezone.now().isoformat()
            }
            
            # Calculate scores
            scores = self._calculate_audit_scores(audit_results)
            
            # Generate AI recommendations
            ai_recommendations = self.ai_service.generate_local_seo_recommendations(audit_results)
            
            # Generate AI summary
            ai_summary = self.ai_service.generate_audit_summary(
                business_profile.__dict__, audit_results
            )
            
            # Update audit report
            audit_report.audit_results = audit_results
            audit_report.overall_score = scores['overall_score']
            audit_report.nap_consistency_score = scores['nap_consistency_score']
            audit_report.profile_completeness_score = scores['profile_completeness_score']
            audit_report.review_health_score = scores['review_health_score']
            audit_report.local_visibility_score = scores['local_visibility_score']
            audit_report.recommendations = ai_recommendations.get('recommendations', [])
            audit_report.ai_summary = ai_summary
            audit_report.priority_tasks = ai_recommendations.get('quick_wins', [])
            audit_report.status = 'completed'
            audit_report.save()
            
            # Create SEO tasks based on recommendations
            self._create_seo_tasks(business_profile, audit_report, ai_recommendations)
            
            logger.info(f"Completed Local SEO audit for {business_profile.business_name}")
            return audit_report
            
        except Exception as e:
            logger.error(f"Error conducting audit for {business_profile.business_name}: {e}")
            audit_report.status = 'failed'
            audit_report.save()
            raise
    
    def _audit_google_business_profile(self, business_profile: BusinessProfile) -> Dict[str, Any]:
        """Audit Google Business Profile completeness and accuracy"""
        logger.info(f"Auditing Google Business Profile for {business_profile.business_name}")
        
        audit_results = {
            'profile_found': False,
            'completeness_score': 0,
            'missing_fields': [],
            'recommendations': []
        }
        
        if business_profile.google_place_id:
            # Get detailed business information from Google
            gbp_data = self.gbp_service.get_business_details(business_profile.google_place_id)
            
            if gbp_data:
                audit_results['profile_found'] = True
                audit_results['google_data'] = gbp_data
                
                # Check completeness
                completeness = self._check_profile_completeness(business_profile, gbp_data)
                audit_results.update(completeness)
                
                # Check data consistency
                consistency = self._check_data_consistency(business_profile, gbp_data)
                audit_results.update(consistency)
            else:
                audit_results['recommendations'].append(
                    "Google Business Profile not found or inaccessible. Verify the Place ID."
                )
        else:
            audit_results['recommendations'].append(
                "Google Place ID not set. Please claim and verify your Google Business Profile."
            )
        
        return audit_results
    
    def _audit_nap_consistency(self, business_profile: BusinessProfile) -> Dict[str, Any]:
        """Audit NAP (Name, Address, Phone) consistency across platforms"""
        logger.info(f"Auditing NAP consistency for {business_profile.business_name}")
        
        # This would typically check NAP across multiple platforms
        # For now, we'll focus on Google Business Profile consistency
        
        audit_results = {
            'consistency_score': 100,  # Start with perfect score
            'issues': [],
            'recommendations': []
        }
        
        # Check if all NAP fields are complete
        if not business_profile.business_name:
            audit_results['issues'].append("Business name is missing")
            audit_results['consistency_score'] -= 20
        
        if not business_profile.address_line_1:
            audit_results['issues'].append("Address is incomplete")
            audit_results['consistency_score'] -= 20
        
        if not business_profile.phone_number:
            audit_results['issues'].append("Phone number is missing")
            audit_results['consistency_score'] -= 20
        
        # Ensure score doesn't go below 0
        audit_results['consistency_score'] = max(0, audit_results['consistency_score'])
        
        if audit_results['issues']:
            audit_results['recommendations'].append(
                "Complete all NAP information to improve local search visibility"
            )
        
        return audit_results
    
    def _audit_reviews(self, business_profile: BusinessProfile) -> Dict[str, Any]:
        """Audit review health and sentiment"""
        logger.info(f"Auditing reviews for {business_profile.business_name}")
        
        audit_results = {
            'review_count': business_profile.total_reviews,
            'average_rating': float(business_profile.average_rating),
            'health_score': 0,
            'sentiment_analysis': {},
            'recommendations': []
        }
        
        # Calculate review health score
        if business_profile.total_reviews == 0:
            audit_results['health_score'] = 0
            audit_results['recommendations'].append(
                "No reviews found. Encourage customers to leave reviews."
            )
        else:
            # Score based on review count and rating
            review_score = min(100, business_profile.total_reviews * 5)  # 5 points per review, max 100
            rating_score = (float(business_profile.average_rating) / 5.0) * 100
            audit_results['health_score'] = int((review_score + rating_score) / 2)
        
        # Get and analyze reviews if Google Place ID is available
        if business_profile.google_place_id:
            reviews = self.gbp_service.get_business_reviews(business_profile.google_place_id)
            if reviews:
                sentiment_analysis = self.ai_service.analyze_review_sentiment(reviews)
                audit_results['sentiment_analysis'] = sentiment_analysis
                audit_results['recent_reviews'] = reviews[:5]  # Store 5 most recent
        
        return audit_results
    
    def _audit_local_visibility(self, business_profile: BusinessProfile) -> Dict[str, Any]:
        """Audit local search visibility"""
        logger.info(f"Auditing local visibility for {business_profile.business_name}")
        
        audit_results = {
            'visibility_score': 0,
            'keyword_rankings': [],
            'local_pack_presence': False,
            'recommendations': []
        }
        
        # Check visibility for primary business category keywords
        if business_profile.primary_category:
            location = f"{business_profile.city}, {business_profile.state}, {business_profile.country}"
            
            # Test a few relevant keywords
            test_keywords = [
                business_profile.primary_category,
                f"{business_profile.primary_category} {business_profile.city}",
                f"{business_profile.primary_category} near me"
            ]
            
            for keyword in test_keywords:
                ranking_data = self.serp_service.track_local_keyword_rankings(
                    keyword, location, business_profile.business_name, 
                    business_profile.google_place_id
                )
                audit_results['keyword_rankings'].append(ranking_data)
                
                if ranking_data.get('is_in_local_pack'):
                    audit_results['local_pack_presence'] = True
        
        # Calculate visibility score
        local_pack_count = sum(1 for r in audit_results['keyword_rankings'] if r.get('is_in_local_pack'))
        if audit_results['keyword_rankings']:
            visibility_percentage = (local_pack_count / len(audit_results['keyword_rankings'])) * 100
            audit_results['visibility_score'] = int(visibility_percentage)
        
        if not audit_results['local_pack_presence']:
            audit_results['recommendations'].append(
                "Business not appearing in local pack results. Optimize Google Business Profile."
            )
        
        return audit_results
    
    def _audit_competitors(self, business_profile: BusinessProfile) -> Dict[str, Any]:
        """Audit competitor landscape"""
        logger.info(f"Auditing competitors for {business_profile.business_name}")
        
        audit_results = {
            'competitors_found': 0,
            'competitive_analysis': {},
            'recommendations': []
        }
        
        if business_profile.google_place_id:
            competitors = self.gbp_service.find_competitors(business_profile.google_place_id)
            audit_results['competitors_found'] = len(competitors)
            audit_results['competitors'] = competitors[:5]  # Top 5 competitors
            
            if competitors:
                # Analyze competitive landscape
                avg_competitor_rating = sum(c.get('rating', 0) for c in competitors) / len(competitors)
                avg_competitor_reviews = sum(c.get('user_ratings_total', 0) for c in competitors) / len(competitors)
                
                audit_results['competitive_analysis'] = {
                    'average_competitor_rating': round(avg_competitor_rating, 2),
                    'average_competitor_reviews': int(avg_competitor_reviews),
                    'business_rating_vs_avg': float(business_profile.average_rating) - avg_competitor_rating,
                    'business_reviews_vs_avg': business_profile.total_reviews - avg_competitor_reviews
                }
        
        return audit_results
    
    def _check_profile_completeness(self, business_profile: BusinessProfile, 
                                  gbp_data: Dict) -> Dict[str, Any]:
        """Check Google Business Profile completeness"""
        required_fields = [
            'name', 'formatted_address', 'formatted_phone_number',
            'website', 'opening_hours', 'types'
        ]
        
        missing_fields = []
        for field in required_fields:
            if not gbp_data.get(field):
                missing_fields.append(field)
        
        completeness_score = int(((len(required_fields) - len(missing_fields)) / len(required_fields)) * 100)
        
        return {
            'completeness_score': completeness_score,
            'missing_fields': missing_fields
        }
    
    def _check_data_consistency(self, business_profile: BusinessProfile, 
                              gbp_data: Dict) -> Dict[str, Any]:
        """Check data consistency between local profile and Google data"""
        inconsistencies = []
        
        # Check name consistency
        if gbp_data.get('name') and gbp_data['name'] != business_profile.business_name:
            inconsistencies.append(f"Business name mismatch: Local='{business_profile.business_name}', Google='{gbp_data['name']}'")
        
        # Check phone consistency
        if gbp_data.get('formatted_phone_number') and gbp_data['formatted_phone_number'] != business_profile.phone_number:
            inconsistencies.append(f"Phone number mismatch")
        
        return {
            'data_inconsistencies': inconsistencies,
            'consistency_issues_count': len(inconsistencies)
        }
    
    def _calculate_audit_scores(self, audit_results: Dict) -> Dict[str, int]:
        """Calculate overall audit scores"""
        gbp_score = audit_results.get('google_business_profile', {}).get('completeness_score', 0)
        nap_score = audit_results.get('nap_consistency', {}).get('consistency_score', 0)
        review_score = audit_results.get('reviews', {}).get('health_score', 0)
        visibility_score = audit_results.get('local_visibility', {}).get('visibility_score', 0)
        
        # Calculate weighted overall score
        overall_score = int((gbp_score * 0.3 + nap_score * 0.2 + review_score * 0.3 + visibility_score * 0.2))
        
        return {
            'overall_score': overall_score,
            'nap_consistency_score': nap_score,
            'profile_completeness_score': gbp_score,
            'review_health_score': review_score,
            'local_visibility_score': visibility_score
        }
    
    def _create_seo_tasks(self, business_profile: BusinessProfile, audit_report: LocalAuditReport,
                         ai_recommendations: Dict):
        """Create SEO tasks based on audit recommendations"""
        recommendations = ai_recommendations.get('recommendations', [])
        
        for rec in recommendations:
            LocalSEOTask.objects.create(
                business_profile=business_profile,
                audit_report=audit_report,
                title=rec.get('title', 'Local SEO Task'),
                description=rec.get('description', ''),
                category=rec.get('category', 'General'),
                priority=rec.get('priority', 'medium'),
                estimated_impact=self._priority_to_impact_score(rec.get('impact', 'medium'))
            )
    
    def _priority_to_impact_score(self, impact: str) -> int:
        """Convert impact level to numeric score"""
        impact_map = {
            'low': 25,
            'medium': 50,
            'high': 75,
            'critical': 90
        }
        return impact_map.get(impact.lower(), 50)
