import logging
import requests
from typing import Dict, List, Optional, Any
from django.conf import settings

# Try to import DataForSEO client, but handle gracefully if not available
try:
    from dataforseo_client import <PERSON>ForSeoApi
    DATAFORSEO_AVAILABLE = True
except ImportError:
    DATAFORSEO_AVAILABLE = False
    DataForSeoApi = None

logger = logging.getLogger(__name__)


class SERPTrackingService:
    """Service for tracking local keyword rankings using DataForSEO API"""

    def __init__(self):
        # Initialize DataForSEO client if credentials are available
        self.dataforseo_login = getattr(settings, 'DATAFORSEO_API_LOGIN', None)
        self.dataforseo_password = getattr(settings, 'DATAFORSEO_API_PASSWORD', None)

        if DATAFORSEO_AVAILABLE and self.dataforseo_login and self.dataforseo_password:
            self.client = DataForSeoApi(self.dataforseo_login, self.dataforseo_password)
        else:
            self.client = None
            if not DATAFORSEO_AVAILABLE:
                logger.warning("DataForSEO client not available. SERP tracking will use fallback methods.")
            else:
                logger.warning("DataForSEO credentials not configured. SERP tracking will use fallback methods.")

    def track_local_keyword_rankings(self, keyword: str, location: str, business_name: str,
                                   place_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Track local keyword rankings for a business

        Args:
            keyword: The keyword to track
            location: Location for local search (e.g., "Sydney, NSW, Australia")
            business_name: Name of the business to look for
            place_id: Google Place ID of the business (optional)

        Returns:
            Dictionary containing ranking information
        """
        try:
            if self.client:
                return self._track_with_dataforseo(keyword, location, business_name, place_id)
            else:
                return self._track_with_fallback(keyword, location, business_name)

        except Exception as e:
            logger.error(f"Error tracking keyword rankings for '{keyword}' in {location}: {e}")
            return self._empty_ranking_result(keyword, location)

    def get_local_pack_results(self, keyword: str, location: str) -> List[Dict]:
        """
        Get local pack results for a keyword and location

        Args:
            keyword: The search keyword
            location: Location for local search

        Returns:
            List of businesses in the local pack
        """
        try:
            if self.client:
                return self._get_local_pack_dataforseo(keyword, location)
            else:
                return self._get_local_pack_fallback(keyword, location)

        except Exception as e:
            logger.error(f"Error getting local pack for '{keyword}' in {location}: {e}")
            return []

    def analyze_serp_features(self, keyword: str, location: str) -> Dict[str, Any]:
        """
        Analyze SERP features for a keyword and location

        Args:
            keyword: The search keyword
            location: Location for local search

        Returns:
            Dictionary containing SERP features analysis
        """
        try:
            if self.client:
                return self._analyze_serp_features_dataforseo(keyword, location)
            else:
                return self._analyze_serp_features_fallback(keyword, location)

        except Exception as e:
            logger.error(f"Error analyzing SERP features for '{keyword}' in {location}: {e}")
            return {}

    def _track_with_dataforseo(self, keyword: str, location: str, business_name: str,
                              place_id: Optional[str] = None) -> Dict[str, Any]:
        """Track rankings using DataForSEO API"""
        try:
            # Set up the task for Google SERP
            post_data = [{
                "keyword": keyword,
                "location_name": location,
                "language_name": "English",
                "device": "desktop",
                "os": "windows"
            }]

            # Submit the task
            response = self.client.serp().google().organic().live().post(post_data)

            if response['status_code'] == 20000:
                results = response.get('tasks', [{}])[0].get('result', [])
                if results:
                    serp_data = results[0]
                    return self._parse_dataforseo_results(serp_data, business_name, place_id)

            logger.warning(f"DataForSEO API returned status code: {response.get('status_code')}")
            return self._empty_ranking_result(keyword, location)

        except Exception as e:
            logger.error(f"DataForSEO API error: {e}")
            return self._track_with_fallback(keyword, location, business_name)

    def _get_local_pack_dataforseo(self, keyword: str, location: str) -> List[Dict]:
        """Get local pack results using DataForSEO API"""
        try:
            post_data = [{
                "keyword": keyword,
                "location_name": location,
                "language_name": "English",
                "device": "desktop"
            }]

            response = self.client.serp().google().maps().live().post(post_data)

            if response['status_code'] == 20000:
                results = response.get('tasks', [{}])[0].get('result', [])
                if results:
                    return self._parse_local_pack_results(results[0])

            return []

        except Exception as e:
            logger.error(f"DataForSEO local pack error: {e}")
            return []

    def _analyze_serp_features_dataforseo(self, keyword: str, location: str) -> Dict[str, Any]:
        """Analyze SERP features using DataForSEO API"""
        try:
            post_data = [{
                "keyword": keyword,
                "location_name": location,
                "language_name": "English",
                "device": "desktop"
            }]

            response = self.client.serp().google().organic().live().post(post_data)

            if response['status_code'] == 20000:
                results = response.get('tasks', [{}])[0].get('result', [])
                if results:
                    return self._parse_serp_features(results[0])

            return {}

        except Exception as e:
            logger.error(f"DataForSEO SERP features error: {e}")
            return {}

    def _track_with_fallback(self, keyword: str, location: str, business_name: str) -> Dict[str, Any]:
        """Fallback method for tracking rankings without DataForSEO"""
        logger.info(f"Using fallback method for tracking '{keyword}' in {location}")

        # This is a simplified fallback - in production, you might want to use
        # other SERP APIs like SerpApi, ScrapingBee, etc.
        return {
            'keyword': keyword,
            'location': location,
            'business_name': business_name,
            'organic_position': None,
            'local_pack_position': None,
            'is_in_local_pack': False,
            'is_in_maps': False,
            'serp_features': [],
            'check_date': None,
            'status': 'fallback_method',
            'message': 'Ranking data not available - configure DataForSEO API for accurate tracking'
        }

    def _get_local_pack_fallback(self, keyword: str, location: str) -> List[Dict]:
        """Fallback method for getting local pack results"""
        logger.info(f"Using fallback method for local pack '{keyword}' in {location}")
        return []

    def _analyze_serp_features_fallback(self, keyword: str, location: str) -> Dict[str, Any]:
        """Fallback method for analyzing SERP features"""
        logger.info(f"Using fallback method for SERP features '{keyword}' in {location}")
        return {
            'has_local_pack': False,
            'has_knowledge_panel': False,
            'has_featured_snippet': False,
            'has_people_also_ask': False,
            'has_images': False,
            'has_videos': False,
            'total_results': 0
        }

    def _parse_dataforseo_results(self, serp_data: Dict, business_name: str,
                                 place_id: Optional[str] = None) -> Dict[str, Any]:
        """Parse DataForSEO SERP results"""
        organic_results = serp_data.get('items', [])
        local_pack = None
        organic_position = None
        local_pack_position = None

        # Look for local pack
        for item in organic_results:
            if item.get('type') == 'local_pack':
                local_pack = item
                break

        # Check local pack for business
        if local_pack and local_pack.get('items'):
            for idx, local_item in enumerate(local_pack['items'], 1):
                if (business_name.lower() in local_item.get('title', '').lower() or
                    (place_id and local_item.get('place_id') == place_id)):
                    local_pack_position = idx
                    break

        # Check organic results for business
        for idx, item in enumerate(organic_results, 1):
            if item.get('type') == 'organic':
                title = item.get('title', '')
                url = item.get('url', '')
                if business_name.lower() in title.lower():
                    organic_position = idx
                    break

        return {
            'keyword': serp_data.get('keyword'),
            'location': serp_data.get('location_name'),
            'business_name': business_name,
            'organic_position': organic_position,
            'local_pack_position': local_pack_position,
            'is_in_local_pack': local_pack_position is not None,
            'is_in_maps': local_pack_position is not None,
            'serp_features': self._extract_serp_features(organic_results),
            'check_date': serp_data.get('datetime'),
            'status': 'success'
        }

    def _parse_local_pack_results(self, results: Dict) -> List[Dict]:
        """Parse local pack results from DataForSEO"""
        local_businesses = []
        items = results.get('items', [])

        for item in items:
            if item.get('type') == 'maps_paid' or item.get('type') == 'maps_organic':
                business = {
                    'name': item.get('title'),
                    'address': item.get('address'),
                    'phone': item.get('phone'),
                    'website': item.get('url'),
                    'rating': item.get('rating', {}).get('rating_value'),
                    'reviews_count': item.get('rating', {}).get('votes_count'),
                    'place_id': item.get('place_id'),
                    'position': item.get('rank_absolute')
                }
                local_businesses.append(business)

        return local_businesses

    def _parse_serp_features(self, results: Dict) -> Dict[str, Any]:
        """Parse SERP features from DataForSEO results"""
        items = results.get('items', [])
        features = {
            'has_local_pack': False,
            'has_knowledge_panel': False,
            'has_featured_snippet': False,
            'has_people_also_ask': False,
            'has_images': False,
            'has_videos': False,
            'total_results': results.get('total_count', 0)
        }

        for item in items:
            item_type = item.get('type', '')
            if item_type == 'local_pack':
                features['has_local_pack'] = True
            elif item_type == 'knowledge_graph':
                features['has_knowledge_panel'] = True
            elif item_type == 'featured_snippet':
                features['has_featured_snippet'] = True
            elif item_type == 'people_also_ask':
                features['has_people_also_ask'] = True
            elif item_type == 'images':
                features['has_images'] = True
            elif item_type == 'video':
                features['has_videos'] = True

        return features

    def _extract_serp_features(self, organic_results: List[Dict]) -> List[str]:
        """Extract SERP features from organic results"""
        features = []
        for item in organic_results:
            item_type = item.get('type', '')
            if item_type not in ['organic'] and item_type not in features:
                features.append(item_type)
        return features

    def _empty_ranking_result(self, keyword: str, location: str) -> Dict[str, Any]:
        """Return empty ranking result structure"""
        return {
            'keyword': keyword,
            'location': location,
            'business_name': '',
            'organic_position': None,
            'local_pack_position': None,
            'is_in_local_pack': False,
            'is_in_maps': False,
            'serp_features': [],
            'check_date': None,
            'status': 'error'
        }
