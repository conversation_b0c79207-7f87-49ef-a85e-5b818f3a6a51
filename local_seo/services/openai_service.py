import logging
import json
from typing import Dict, List, Optional, Any
from django.conf import settings
import openai
from openai import OpenAI

logger = logging.getLogger(__name__)


class OpenAILocalSEOService:
    """Service for AI-powered Local SEO analysis using OpenAI GPT-4"""
    
    def __init__(self):
        self.client = OpenAI(api_key=settings.OPENAI_API_KEY)
        self.model = settings.OPENAI_MODEL
        self.max_tokens = settings.OPENAI_MAX_TOKENS
    
    def analyze_review_sentiment(self, reviews: List[Dict]) -> Dict[str, Any]:
        """
        Analyze sentiment and extract topics from business reviews
        
        Args:
            reviews: List of review dictionaries
            
        Returns:
            Dictionary containing sentiment analysis and topic extraction
        """
        if not reviews:
            return {
                'overall_sentiment': 'neutral',
                'sentiment_score': 0.0,
                'topics': [],
                'summary': 'No reviews available for analysis.'
            }
        
        try:
            # Prepare review text for analysis
            review_texts = []
            for review in reviews[:20]:  # Limit to 20 most recent reviews
                text = review.get('text', '').strip()
                rating = review.get('rating', 0)
                if text:
                    review_texts.append(f"Rating: {rating}/5 - {text}")
            
            if not review_texts:
                return {
                    'overall_sentiment': 'neutral',
                    'sentiment_score': 0.0,
                    'topics': [],
                    'summary': 'No review text available for analysis.'
                }
            
            reviews_text = "\n\n".join(review_texts)
            
            prompt = f"""
            Analyze the following customer reviews for a local business and provide:
            
            1. Overall sentiment (positive, neutral, or negative)
            2. Sentiment score (-1 to 1, where -1 is very negative, 0 is neutral, 1 is very positive)
            3. Key topics mentioned (max 10 topics)
            4. Common issues or complaints (if any)
            5. Common praise points (if any)
            6. Brief summary of customer feedback
            
            Reviews:
            {reviews_text}
            
            Please respond in JSON format with the following structure:
            {{
                "overall_sentiment": "positive|neutral|negative",
                "sentiment_score": 0.0,
                "topics": ["topic1", "topic2", ...],
                "issues": ["issue1", "issue2", ...],
                "praise": ["praise1", "praise2", ...],
                "summary": "Brief summary of customer feedback"
            }}
            """
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert in sentiment analysis and customer feedback analysis for local businesses."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=0.3
            )
            
            result = json.loads(response.choices[0].message.content)
            logger.info(f"Successfully analyzed sentiment for {len(reviews)} reviews")
            return result
            
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing OpenAI response: {e}")
            return self._fallback_sentiment_analysis(reviews)
        except Exception as e:
            logger.error(f"Error analyzing review sentiment: {e}")
            return self._fallback_sentiment_analysis(reviews)
    
    def generate_local_seo_recommendations(self, audit_data: Dict) -> Dict[str, Any]:
        """
        Generate AI-powered local SEO recommendations based on audit data
        
        Args:
            audit_data: Dictionary containing audit results
            
        Returns:
            Dictionary containing recommendations and priority tasks
        """
        try:
            prompt = f"""
            Based on the following local SEO audit data for a business, provide specific, actionable recommendations:
            
            Audit Data:
            {json.dumps(audit_data, indent=2)}
            
            Please provide:
            1. Top 5 priority recommendations with specific actions
            2. Estimated impact (high, medium, low) for each recommendation
            3. Estimated effort (easy, moderate, difficult) for each recommendation
            4. Overall strategy summary
            5. Quick wins (tasks that can be completed quickly with good impact)
            
            Focus on Australian local SEO best practices and Google Business Profile optimization.
            
            Please respond in JSON format with the following structure:
            {{
                "recommendations": [
                    {{
                        "title": "Recommendation title",
                        "description": "Detailed description",
                        "category": "Category (e.g., NAP, Reviews, Content)",
                        "impact": "high|medium|low",
                        "effort": "easy|moderate|difficult",
                        "priority": "critical|high|medium|low"
                    }}
                ],
                "strategy_summary": "Overall strategy summary",
                "quick_wins": ["Quick win 1", "Quick win 2", ...],
                "estimated_timeline": "Estimated timeline for implementation"
            }}
            """
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert Local SEO consultant specializing in Australian businesses and Google Business Profile optimization."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=0.3
            )
            
            result = json.loads(response.choices[0].message.content)
            logger.info("Successfully generated local SEO recommendations")
            return result
            
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing OpenAI recommendations response: {e}")
            return self._fallback_recommendations()
        except Exception as e:
            logger.error(f"Error generating local SEO recommendations: {e}")
            return self._fallback_recommendations()
    
    def generate_audit_summary(self, business_profile: Dict, audit_results: Dict) -> str:
        """
        Generate a natural language summary of the local SEO audit
        
        Args:
            business_profile: Business profile information
            audit_results: Complete audit results
            
        Returns:
            Natural language summary of the audit
        """
        try:
            business_name = business_profile.get('business_name', 'the business')
            
            prompt = f"""
            Create a comprehensive but concise audit summary for {business_name} based on the following data:
            
            Business Profile:
            {json.dumps(business_profile, indent=2)}
            
            Audit Results:
            {json.dumps(audit_results, indent=2)}
            
            Write a professional summary that:
            1. Highlights the current local SEO performance
            2. Identifies key strengths and weaknesses
            3. Provides context for the scores
            4. Suggests next steps
            5. Uses clear, non-technical language suitable for business owners
            
            Keep the summary to 3-4 paragraphs maximum.
            """
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a Local SEO expert writing audit summaries for Australian business owners."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=600,
                temperature=0.4
            )
            
            summary = response.choices[0].message.content.strip()
            logger.info(f"Successfully generated audit summary for {business_name}")
            return summary
            
        except Exception as e:
            logger.error(f"Error generating audit summary: {e}")
            return f"Local SEO audit completed for {business_profile.get('business_name', 'your business')}. Please review the detailed scores and recommendations for specific improvement areas."
    
    def extract_review_topics(self, review_text: str) -> List[str]:
        """
        Extract key topics from a single review
        
        Args:
            review_text: The review text to analyze
            
        Returns:
            List of extracted topics
        """
        if not review_text or len(review_text.strip()) < 10:
            return []
        
        try:
            prompt = f"""
            Extract the main topics mentioned in this customer review. Focus on:
            - Service aspects (quality, speed, friendliness, etc.)
            - Product aspects (quality, variety, price, etc.)
            - Location aspects (parking, accessibility, cleanliness, etc.)
            - Experience aspects (wait time, atmosphere, convenience, etc.)
            
            Review: "{review_text}"
            
            Return only a JSON array of topics (max 5 topics):
            ["topic1", "topic2", "topic3"]
            """
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert at extracting key topics from customer reviews."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=100,
                temperature=0.2
            )
            
            topics = json.loads(response.choices[0].message.content)
            return topics if isinstance(topics, list) else []
            
        except Exception as e:
            logger.error(f"Error extracting review topics: {e}")
            return []
    
    def _fallback_sentiment_analysis(self, reviews: List[Dict]) -> Dict[str, Any]:
        """Fallback sentiment analysis when OpenAI fails"""
        if not reviews:
            return {
                'overall_sentiment': 'neutral',
                'sentiment_score': 0.0,
                'topics': [],
                'summary': 'No reviews available for analysis.'
            }
        
        # Simple rating-based sentiment analysis
        total_rating = sum(review.get('rating', 0) for review in reviews)
        avg_rating = total_rating / len(reviews) if reviews else 0
        
        if avg_rating >= 4:
            sentiment = 'positive'
            score = 0.5
        elif avg_rating >= 3:
            sentiment = 'neutral'
            score = 0.0
        else:
            sentiment = 'negative'
            score = -0.5
        
        return {
            'overall_sentiment': sentiment,
            'sentiment_score': score,
            'topics': ['service', 'quality', 'experience'],
            'summary': f'Based on {len(reviews)} reviews with an average rating of {avg_rating:.1f}/5.'
        }
    
    def _fallback_recommendations(self) -> Dict[str, Any]:
        """Fallback recommendations when OpenAI fails"""
        return {
            'recommendations': [
                {
                    'title': 'Complete Google Business Profile',
                    'description': 'Ensure all business information is complete and accurate',
                    'category': 'Profile Completeness',
                    'impact': 'high',
                    'effort': 'easy',
                    'priority': 'high'
                },
                {
                    'title': 'Encourage Customer Reviews',
                    'description': 'Implement a system to request reviews from satisfied customers',
                    'category': 'Reviews',
                    'impact': 'high',
                    'effort': 'moderate',
                    'priority': 'high'
                }
            ],
            'strategy_summary': 'Focus on completing your Google Business Profile and building a strong review base.',
            'quick_wins': ['Update business hours', 'Add business photos', 'Respond to existing reviews'],
            'estimated_timeline': '2-4 weeks for initial improvements'
        }
