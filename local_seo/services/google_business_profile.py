import logging
import requests
from typing import Dict, List, Optional, Any
from django.conf import settings
from googlemaps import Client as GoogleMapsClient
from googlemaps.exceptions import ApiError
import json

logger = logging.getLogger(__name__)


class GoogleBusinessProfileService:
    """Service for interacting with Google Business Profile API"""

    def __init__(self):
        self.api_key = settings.GOOGLE_BUSINESS_PROFILE_API_KEY
        # Use the Google API key that's available in settings
        google_api_key = getattr(settings, 'GOOGLE_API_KEY', None)
        if not google_api_key:
            # Fallback to the business profile API key
            google_api_key = settings.GOOGLE_BUSINESS_PROFILE_API_KEY
        self.maps_client = GoogleMapsClient(key=google_api_key)
        self.base_url = "https://mybusinessbusinessinformation.googleapis.com/v1"

    def search_business_by_name_and_location(self, business_name: str, location: str) -> List[Dict]:
        """
        Search for businesses using Google Places API

        Args:
            business_name: Name of the business to search for
            location: Location to search in (e.g., "Sydney, NSW, Australia")

        Returns:
            List of business profiles found
        """
        try:
            # Use Google Places Text Search
            places_result = self.maps_client.places(
                query=f"{business_name} {location}",
                type='establishment'
            )

            businesses = []
            for place in places_result.get('results', []):
                business_data = self._extract_business_data_from_place(place)
                businesses.append(business_data)

            logger.info(f"Found {len(businesses)} businesses for query: {business_name} in {location}")
            return businesses

        except ApiError as e:
            logger.error(f"Google Places API error: {e}")
            return []
        except Exception as e:
            logger.error(f"Error searching for business: {e}")
            return []

    def get_business_details(self, place_id: str) -> Optional[Dict]:
        """
        Get detailed business information using Google Places API

        Args:
            place_id: Google Place ID

        Returns:
            Detailed business information or None if not found
        """
        try:
            # Get place details
            place_details = self.maps_client.place(
                place_id=place_id,
                fields=[
                    'name', 'formatted_address', 'formatted_phone_number',
                    'website', 'rating', 'user_ratings_total', 'reviews',
                    'opening_hours', 'types', 'geometry', 'photos',
                    'business_status', 'price_level'
                ]
            )

            if place_details.get('status') == 'OK':
                return self._extract_detailed_business_data(place_details['result'])
            else:
                logger.warning(f"Place details not found for place_id: {place_id}")
                return None

        except ApiError as e:
            logger.error(f"Google Places API error for place_id {place_id}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error getting business details for place_id {place_id}: {e}")
            return None

    def get_business_reviews(self, place_id: str) -> List[Dict]:
        """
        Get business reviews using Google Places API

        Args:
            place_id: Google Place ID

        Returns:
            List of reviews
        """
        try:
            place_details = self.maps_client.place(
                place_id=place_id,
                fields=['reviews']
            )

            if place_details.get('status') == 'OK':
                reviews = place_details['result'].get('reviews', [])
                return [self._extract_review_data(review) for review in reviews]
            else:
                logger.warning(f"Reviews not found for place_id: {place_id}")
                return []

        except ApiError as e:
            logger.error(f"Google Places API error getting reviews for {place_id}: {e}")
            return []
        except Exception as e:
            logger.error(f"Error getting reviews for place_id {place_id}: {e}")
            return []

    def find_competitors(self, place_id: str, radius: int = 5000) -> List[Dict]:
        """
        Find competitor businesses near a given location

        Args:
            place_id: Google Place ID of the main business
            radius: Search radius in meters (default 5km)

        Returns:
            List of competitor businesses
        """
        try:
            # First get the main business details to get location and type
            main_business = self.get_business_details(place_id)
            if not main_business:
                return []

            location = main_business.get('geometry', {}).get('location')
            business_types = main_business.get('types', [])

            if not location:
                logger.warning(f"No location found for place_id: {place_id}")
                return []

            # Search for nearby businesses of similar type
            competitors = []
            for business_type in business_types[:3]:  # Limit to first 3 types
                try:
                    nearby_places = self.maps_client.places_nearby(
                        location=(location['lat'], location['lng']),
                        radius=radius,
                        type=business_type
                    )

                    for place in nearby_places.get('results', []):
                        if place.get('place_id') != place_id:  # Exclude the main business
                            competitor_data = self._extract_business_data_from_place(place)
                            competitor_data['distance_km'] = self._calculate_distance(
                                location, place.get('geometry', {}).get('location', {})
                            )
                            competitors.append(competitor_data)

                except ApiError as e:
                    logger.warning(f"Error searching for competitors of type {business_type}: {e}")
                    continue

            # Remove duplicates and sort by rating/reviews
            unique_competitors = {}
            for comp in competitors:
                place_id = comp.get('place_id')
                if place_id and place_id not in unique_competitors:
                    unique_competitors[place_id] = comp

            sorted_competitors = sorted(
                unique_competitors.values(),
                key=lambda x: (x.get('rating', 0), x.get('user_ratings_total', 0)),
                reverse=True
            )

            logger.info(f"Found {len(sorted_competitors)} competitors for place_id: {place_id}")
            return sorted_competitors[:10]  # Return top 10 competitors

        except Exception as e:
            logger.error(f"Error finding competitors for place_id {place_id}: {e}")
            return []

    def _extract_business_data_from_place(self, place: Dict) -> Dict:
        """Extract standardized business data from Google Places result"""
        return {
            'place_id': place.get('place_id'),
            'name': place.get('name'),
            'formatted_address': place.get('formatted_address'),
            'formatted_phone_number': place.get('formatted_phone_number'),
            'website': place.get('website'),
            'rating': place.get('rating', 0),
            'user_ratings_total': place.get('user_ratings_total', 0),
            'types': place.get('types', []),
            'geometry': place.get('geometry'),
            'business_status': place.get('business_status'),
            'price_level': place.get('price_level'),
            'photos': [photo.get('photo_reference') for photo in place.get('photos', [])[:5]]
        }

    def _extract_detailed_business_data(self, place: Dict) -> Dict:
        """Extract detailed business data from Google Places details result"""
        data = self._extract_business_data_from_place(place)

        # Add detailed information
        data.update({
            'opening_hours': self._extract_opening_hours(place.get('opening_hours')),
            'reviews': [self._extract_review_data(review) for review in place.get('reviews', [])],
            'utc_offset': place.get('utc_offset'),
        })

        return data

    def _extract_review_data(self, review: Dict) -> Dict:
        """Extract standardized review data from Google Places review"""
        return {
            'author_name': review.get('author_name'),
            'author_url': review.get('author_url'),
            'profile_photo_url': review.get('profile_photo_url'),
            'rating': review.get('rating'),
            'relative_time_description': review.get('relative_time_description'),
            'text': review.get('text'),
            'time': review.get('time'),
        }

    def _extract_opening_hours(self, opening_hours: Optional[Dict]) -> Dict:
        """Extract and format opening hours data"""
        if not opening_hours:
            return {}

        return {
            'open_now': opening_hours.get('open_now'),
            'periods': opening_hours.get('periods', []),
            'weekday_text': opening_hours.get('weekday_text', []),
        }

    def _calculate_distance(self, location1: Dict, location2: Dict) -> float:
        """Calculate distance between two locations in kilometers"""
        try:
            from math import radians, cos, sin, asin, sqrt

            lat1, lon1 = location1.get('lat', 0), location1.get('lng', 0)
            lat2, lon2 = location2.get('lat', 0), location2.get('lng', 0)

            # Haversine formula
            lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
            dlat = lat2 - lat1
            dlon = lon2 - lon1
            a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
            c = 2 * asin(sqrt(a))
            r = 6371  # Radius of earth in kilometers

            return round(c * r, 2)
        except Exception:
            return 0.0
