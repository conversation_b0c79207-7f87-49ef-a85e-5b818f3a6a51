# Generated by Django 5.2 on 2025-05-27 02:09

import django.contrib.postgres.fields
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BusinessProfile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('business_name', models.CharField(max_length=255, verbose_name='Business Name')),
                ('google_place_id', models.CharField(blank=True, max_length=255, null=True, unique=True, verbose_name='Google Place ID')),
                ('google_business_profile_url', models.URLField(blank=True, null=True, verbose_name='Google Business Profile URL')),
                ('address_line_1', models.CharField(max_length=255, verbose_name='Address Line 1')),
                ('address_line_2', models.CharField(blank=True, max_length=255, verbose_name='Address Line 2')),
                ('city', models.CharField(max_length=100, verbose_name='City')),
                ('state', models.CharField(max_length=100, verbose_name='State/Province')),
                ('postal_code', models.CharField(max_length=20, verbose_name='Postal Code')),
                ('country', models.CharField(default='Australia', max_length=100, verbose_name='Country')),
                ('phone_number', models.CharField(max_length=20, verbose_name='Phone Number')),
                ('website_url', models.URLField(blank=True, null=True, verbose_name='Website URL')),
                ('business_type', models.CharField(choices=[('restaurant', 'Restaurant'), ('retail', 'Retail Store'), ('service', 'Service Business'), ('healthcare', 'Healthcare'), ('automotive', 'Automotive'), ('beauty', 'Beauty & Spa'), ('professional', 'Professional Services'), ('real_estate', 'Real Estate'), ('education', 'Education'), ('entertainment', 'Entertainment'), ('other', 'Other')], max_length=50, verbose_name='Business Type')),
                ('primary_category', models.CharField(blank=True, max_length=255, null=True, verbose_name='Primary Category')),
                ('additional_categories', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=255), blank=True, default=list, help_text='Additional business categories', size=10)),
                ('business_hours', models.JSONField(blank=True, default=dict, verbose_name='Business Hours')),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('suspended', 'Suspended'), ('pending', 'Pending Verification')], default='active', max_length=20, verbose_name='Status')),
                ('is_verified', models.BooleanField(default=False, verbose_name='Is Verified')),
                ('total_reviews', models.PositiveIntegerField(default=0, verbose_name='Total Reviews')),
                ('average_rating', models.DecimalField(decimal_places=2, default=0.0, max_digits=3, verbose_name='Average Rating')),
                ('profile_completeness_score', models.PositiveIntegerField(default=0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='Profile Completeness Score')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('last_synced_at', models.DateTimeField(blank=True, null=True, verbose_name='Last Synced At')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='business_profiles', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Business Profile',
                'verbose_name_plural': 'Business Profiles',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CompetitorProfile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('competitor_name', models.CharField(max_length=255, verbose_name='Competitor Name')),
                ('google_place_id', models.CharField(max_length=255, unique=True, verbose_name='Google Place ID')),
                ('google_business_profile_url', models.URLField(blank=True, null=True, verbose_name='Google Business Profile URL')),
                ('address', models.TextField(verbose_name='Address')),
                ('phone_number', models.CharField(blank=True, max_length=20, verbose_name='Phone Number')),
                ('website_url', models.URLField(blank=True, null=True, verbose_name='Website URL')),
                ('primary_category', models.CharField(blank=True, max_length=255, null=True, verbose_name='Primary Category')),
                ('additional_categories', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=255), blank=True, default=list, size=10)),
                ('total_reviews', models.PositiveIntegerField(default=0, verbose_name='Total Reviews')),
                ('average_rating', models.DecimalField(decimal_places=2, default=0.0, max_digits=3, verbose_name='Average Rating')),
                ('distance_km', models.DecimalField(decimal_places=2, default=0.0, max_digits=5, verbose_name='Distance (KM)')),
                ('competition_score', models.PositiveIntegerField(default=0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='Competition Score')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('last_analyzed_at', models.DateTimeField(blank=True, null=True, verbose_name='Last Analyzed At')),
                ('business_profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='competitors', to='local_seo.businessprofile')),
            ],
            options={
                'verbose_name': 'Competitor Profile',
                'verbose_name_plural': 'Competitor Profiles',
                'ordering': ['-competition_score', '-total_reviews'],
            },
        ),
        migrations.CreateModel(
            name='LocalAuditReport',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('audit_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Audit Date')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=20, verbose_name='Status')),
                ('overall_score', models.PositiveIntegerField(default=0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='Overall Score')),
                ('nap_consistency_score', models.PositiveIntegerField(default=0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='NAP Consistency Score')),
                ('profile_completeness_score', models.PositiveIntegerField(default=0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='Profile Completeness Score')),
                ('review_health_score', models.PositiveIntegerField(default=0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='Review Health Score')),
                ('local_visibility_score', models.PositiveIntegerField(default=0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='Local Visibility Score')),
                ('audit_results', models.JSONField(default=dict, verbose_name='Audit Results')),
                ('recommendations', models.JSONField(default=list, verbose_name='Recommendations')),
                ('ai_summary', models.TextField(blank=True, verbose_name='AI Summary')),
                ('priority_tasks', models.JSONField(default=list, verbose_name='Priority Tasks')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('business_profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='audit_reports', to='local_seo.businessprofile')),
            ],
            options={
                'verbose_name': 'Local Audit Report',
                'verbose_name_plural': 'Local Audit Reports',
                'ordering': ['-audit_date'],
            },
        ),
        migrations.CreateModel(
            name='LocalKeyword',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('keyword', models.CharField(max_length=255, verbose_name='Keyword')),
                ('search_volume', models.PositiveIntegerField(default=0, verbose_name='Monthly Search Volume')),
                ('competition_level', models.CharField(default='medium', max_length=20, verbose_name='Competition Level')),
                ('intent', models.CharField(choices=[('informational', 'Informational'), ('navigational', 'Navigational'), ('transactional', 'Transactional'), ('local', 'Local')], default='local', max_length=20, verbose_name='Search Intent')),
                ('target_location', models.CharField(max_length=255, verbose_name='Target Location')),
                ('radius_km', models.PositiveIntegerField(default=10, verbose_name='Radius (KM)')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('business_profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='keywords', to='local_seo.businessprofile')),
            ],
            options={
                'verbose_name': 'Local Keyword',
                'verbose_name_plural': 'Local Keywords',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='KeywordRanking',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('ranking_type', models.CharField(choices=[('local_pack', 'Local Pack'), ('organic', 'Organic'), ('maps', 'Google Maps')], max_length=20, verbose_name='Ranking Type')),
                ('position', models.PositiveIntegerField(blank=True, null=True, verbose_name='Position')),
                ('is_in_local_pack', models.BooleanField(default=False, verbose_name='In Local Pack')),
                ('is_in_maps', models.BooleanField(default=False, verbose_name='In Google Maps')),
                ('has_reviews', models.BooleanField(default=False, verbose_name='Has Reviews')),
                ('has_photos', models.BooleanField(default=False, verbose_name='Has Photos')),
                ('has_website_link', models.BooleanField(default=False, verbose_name='Has Website Link')),
                ('check_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Check Date')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('keyword', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rankings', to='local_seo.localkeyword')),
            ],
            options={
                'verbose_name': 'Keyword Ranking',
                'verbose_name_plural': 'Keyword Rankings',
                'ordering': ['-check_date'],
            },
        ),
        migrations.CreateModel(
            name='LocalSEOTask',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=255, verbose_name='Task Title')),
                ('description', models.TextField(verbose_name='Description')),
                ('category', models.CharField(max_length=100, verbose_name='Category')),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], default='medium', max_length=20, verbose_name='Priority')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('ignored', 'Ignored')], default='pending', max_length=20, verbose_name='Status')),
                ('estimated_impact', models.PositiveIntegerField(default=0, help_text='Estimated impact on local SEO score (0-100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='Estimated Impact')),
                ('estimated_effort', models.CharField(blank=True, max_length=50, verbose_name='Estimated Effort')),
                ('due_date', models.DateTimeField(blank=True, null=True, verbose_name='Due Date')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='Completed At')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_seo_tasks', to=settings.AUTH_USER_MODEL)),
                ('audit_report', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to='local_seo.localauditreport')),
                ('business_profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='seo_tasks', to='local_seo.businessprofile')),
            ],
            options={
                'verbose_name': 'Local SEO Task',
                'verbose_name_plural': 'Local SEO Tasks',
                'ordering': ['-priority', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Review',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('google_review_id', models.CharField(max_length=255, unique=True, verbose_name='Google Review ID')),
                ('reviewer_name', models.CharField(max_length=255, verbose_name='Reviewer Name')),
                ('reviewer_photo_url', models.URLField(blank=True, null=True, verbose_name='Reviewer Photo URL')),
                ('rating', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Rating')),
                ('review_text', models.TextField(blank=True, verbose_name='Review Text')),
                ('review_date', models.DateTimeField(verbose_name='Review Date')),
                ('business_response', models.TextField(blank=True, verbose_name='Business Response')),
                ('response_date', models.DateTimeField(blank=True, null=True, verbose_name='Response Date')),
                ('sentiment', models.CharField(blank=True, choices=[('positive', 'Positive'), ('neutral', 'Neutral'), ('negative', 'Negative')], max_length=20, null=True, verbose_name='Sentiment')),
                ('sentiment_score', models.DecimalField(blank=True, decimal_places=3, help_text='AI-generated sentiment score (-1 to 1)', max_digits=4, null=True, verbose_name='Sentiment Score')),
                ('key_topics', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=100), blank=True, default=list, help_text='AI-extracted key topics from review', size=10)),
                ('is_flagged', models.BooleanField(default=False, verbose_name='Is Flagged')),
                ('flagged_reason', models.CharField(blank=True, max_length=255, verbose_name='Flagged Reason')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('business_profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='local_seo.businessprofile')),
            ],
            options={
                'verbose_name': 'Review',
                'verbose_name_plural': 'Reviews',
                'ordering': ['-review_date'],
            },
        ),
        migrations.CreateModel(
            name='SentimentTag',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('tag_name', models.CharField(max_length=100, verbose_name='Tag Name')),
                ('tag_type', models.CharField(choices=[('topic', 'Topic'), ('emotion', 'Emotion'), ('issue', 'Issue'), ('praise', 'Praise')], max_length=20, verbose_name='Tag Type')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('mention_count', models.PositiveIntegerField(default=0, verbose_name='Mention Count')),
                ('sentiment_score', models.DecimalField(decimal_places=3, default=0.0, max_digits=4, verbose_name='Average Sentiment Score')),
                ('first_mentioned', models.DateTimeField(blank=True, null=True, verbose_name='First Mentioned')),
                ('last_mentioned', models.DateTimeField(blank=True, null=True, verbose_name='Last Mentioned')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('business_profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sentiment_tags', to='local_seo.businessprofile')),
            ],
            options={
                'verbose_name': 'Sentiment Tag',
                'verbose_name_plural': 'Sentiment Tags',
                'ordering': ['-mention_count', '-last_mentioned'],
            },
        ),
        migrations.AddIndex(
            model_name='businessprofile',
            index=models.Index(fields=['user', 'status'], name='local_seo_b_user_id_e4bcf5_idx'),
        ),
        migrations.AddIndex(
            model_name='businessprofile',
            index=models.Index(fields=['google_place_id'], name='local_seo_b_google__1fd7f0_idx'),
        ),
        migrations.AddIndex(
            model_name='businessprofile',
            index=models.Index(fields=['city', 'state'], name='local_seo_b_city_66df14_idx'),
        ),
        migrations.AddIndex(
            model_name='competitorprofile',
            index=models.Index(fields=['business_profile', 'is_active'], name='local_seo_c_busines_981a62_idx'),
        ),
        migrations.AddIndex(
            model_name='competitorprofile',
            index=models.Index(fields=['google_place_id'], name='local_seo_c_google__84050d_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='competitorprofile',
            unique_together={('business_profile', 'google_place_id')},
        ),
        migrations.AddIndex(
            model_name='localauditreport',
            index=models.Index(fields=['business_profile', 'audit_date'], name='local_seo_l_busines_b37008_idx'),
        ),
        migrations.AddIndex(
            model_name='localauditreport',
            index=models.Index(fields=['status'], name='local_seo_l_status_975f78_idx'),
        ),
        migrations.AddIndex(
            model_name='localkeyword',
            index=models.Index(fields=['business_profile', 'is_active'], name='local_seo_l_busines_b9a791_idx'),
        ),
        migrations.AddIndex(
            model_name='localkeyword',
            index=models.Index(fields=['keyword'], name='local_seo_l_keyword_59cc93_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='localkeyword',
            unique_together={('business_profile', 'keyword', 'target_location')},
        ),
        migrations.AddIndex(
            model_name='keywordranking',
            index=models.Index(fields=['keyword', 'check_date'], name='local_seo_k_keyword_d878ff_idx'),
        ),
        migrations.AddIndex(
            model_name='keywordranking',
            index=models.Index(fields=['ranking_type', 'position'], name='local_seo_k_ranking_9e5922_idx'),
        ),
        migrations.AddIndex(
            model_name='localseotask',
            index=models.Index(fields=['business_profile', 'status'], name='local_seo_l_busines_c9cb0b_idx'),
        ),
        migrations.AddIndex(
            model_name='localseotask',
            index=models.Index(fields=['priority', 'status'], name='local_seo_l_priorit_5dee38_idx'),
        ),
        migrations.AddIndex(
            model_name='localseotask',
            index=models.Index(fields=['assigned_to', 'status'], name='local_seo_l_assigne_e1ed11_idx'),
        ),
        migrations.AddIndex(
            model_name='review',
            index=models.Index(fields=['business_profile', 'review_date'], name='local_seo_r_busines_13401f_idx'),
        ),
        migrations.AddIndex(
            model_name='review',
            index=models.Index(fields=['rating', 'sentiment'], name='local_seo_r_rating_212a03_idx'),
        ),
        migrations.AddIndex(
            model_name='review',
            index=models.Index(fields=['google_review_id'], name='local_seo_r_google__3168bf_idx'),
        ),
        migrations.AddIndex(
            model_name='sentimenttag',
            index=models.Index(fields=['business_profile', 'tag_type'], name='local_seo_s_busines_0b9af6_idx'),
        ),
        migrations.AddIndex(
            model_name='sentimenttag',
            index=models.Index(fields=['tag_name'], name='local_seo_s_tag_nam_cbbcff_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='sentimenttag',
            unique_together={('business_profile', 'tag_name', 'tag_type')},
        ),
    ]
