from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views
from .demo_views import LocalSEODemoView, demo_app_js

app_name = 'local_seo'

urlpatterns = [
    # Business Profile URLs
    path('profiles/', views.BusinessProfileListCreateView.as_view(), name='business-profile-list'),
    path('profiles/<uuid:pk>/', views.BusinessProfileDetailView.as_view(), name='business-profile-detail'),
    path('profiles/search/', views.BusinessProfileSearchView.as_view(), name='business-profile-search'),

    # Local SEO Audit URLs
    path('profiles/<uuid:business_profile_id>/audit/', views.LocalAuditCreateView.as_view(), name='local-audit-create'),
    path('profiles/<uuid:business_profile_id>/audits/', views.LocalAuditReportListView.as_view(), name='local-audit-list'),
    path('audits/<uuid:pk>/', views.LocalAuditReportDetailView.as_view(), name='local-audit-detail'),

    # Keyword Tracking URLs
    path('profiles/<uuid:business_profile_id>/keywords/', views.LocalKeywordListCreateView.as_view(), name='local-keyword-list'),
    path('keywords/<uuid:keyword_id>/track/', views.KeywordRankingTrackingView.as_view(), name='keyword-ranking-track'),
    path('keywords/<uuid:keyword_id>/history/', views.KeywordRankingHistoryView.as_view(), name='keyword-ranking-history'),

    # Competitor Analysis URLs
    path('profiles/<uuid:business_profile_id>/competitors/', views.CompetitorAnalysisView.as_view(), name='competitor-analysis'),

    # Review Analysis URLs
    path('profiles/<uuid:business_profile_id>/reviews/', views.ReviewAnalysisView.as_view(), name='review-analysis'),

    # SEO Tasks URLs
    path('profiles/<uuid:business_profile_id>/tasks/', views.LocalSEOTaskListView.as_view(), name='seo-task-list'),
    path('tasks/<uuid:pk>/', views.LocalSEOTaskUpdateView.as_view(), name='seo-task-update'),

    # Dashboard URLs
    path('dashboard/', views.dashboard_overview, name='dashboard-overview'),

    # Demo URLs
    path('demo/', LocalSEODemoView.as_view(), name='demo'),
    path('demo/app.js', demo_app_js, name='demo-js'),
]
