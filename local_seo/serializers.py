from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    BusinessProfile, LocalKeyword, KeywordRanking, CompetitorProfile,
    Review, LocalAuditReport, SentimentTag, LocalSEOTask
)

User = get_user_model()


class BusinessProfileSerializer(serializers.ModelSerializer):
    """Serializer for BusinessProfile model"""
    
    full_address = serializers.ReadOnlyField()
    user_email = serializers.CharField(source='user.email', read_only=True)
    
    class Meta:
        model = BusinessProfile
        fields = [
            'id', 'user', 'user_email', 'business_name', 'google_place_id',
            'google_business_profile_url', 'address_line_1', 'address_line_2',
            'city', 'state', 'postal_code', 'country', 'phone_number',
            'website_url', 'business_type', 'primary_category',
            'additional_categories', 'business_hours', 'status', 'is_verified',
            'total_reviews', 'average_rating', 'profile_completeness_score',
            'full_address', 'created_at', 'updated_at', 'last_synced_at'
        ]
        read_only_fields = [
            'id', 'user_email', 'profile_completeness_score', 'created_at',
            'updated_at', 'last_synced_at', 'full_address'
        ]
    
    def validate_phone_number(self, value):
        """Validate phone number format"""
        if value and not value.replace(' ', '').replace('-', '').replace('(', '').replace(')', '').replace('+', '').isdigit():
            raise serializers.ValidationError("Please enter a valid phone number.")
        return value


class BusinessProfileCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating BusinessProfile"""
    
    class Meta:
        model = BusinessProfile
        fields = [
            'business_name', 'address_line_1', 'address_line_2', 'city',
            'state', 'postal_code', 'country', 'phone_number', 'website_url',
            'business_type', 'primary_category', 'additional_categories',
            'business_hours'
        ]
    
    def create(self, validated_data):
        # Set the user from the request context
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class LocalKeywordSerializer(serializers.ModelSerializer):
    """Serializer for LocalKeyword model"""
    
    business_name = serializers.CharField(source='business_profile.business_name', read_only=True)
    
    class Meta:
        model = LocalKeyword
        fields = [
            'id', 'business_profile', 'business_name', 'keyword', 'search_volume',
            'competition_level', 'intent', 'target_location', 'radius_km',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'business_name', 'created_at', 'updated_at']


class KeywordRankingSerializer(serializers.ModelSerializer):
    """Serializer for KeywordRanking model"""
    
    keyword_text = serializers.CharField(source='keyword.keyword', read_only=True)
    target_location = serializers.CharField(source='keyword.target_location', read_only=True)
    
    class Meta:
        model = KeywordRanking
        fields = [
            'id', 'keyword', 'keyword_text', 'target_location', 'ranking_type',
            'position', 'is_in_local_pack', 'is_in_maps', 'has_reviews',
            'has_photos', 'has_website_link', 'check_date', 'created_at'
        ]
        read_only_fields = ['id', 'keyword_text', 'target_location', 'created_at']


class CompetitorProfileSerializer(serializers.ModelSerializer):
    """Serializer for CompetitorProfile model"""
    
    business_name = serializers.CharField(source='business_profile.business_name', read_only=True)
    
    class Meta:
        model = CompetitorProfile
        fields = [
            'id', 'business_profile', 'business_name', 'competitor_name',
            'google_place_id', 'google_business_profile_url', 'address',
            'phone_number', 'website_url', 'primary_category',
            'additional_categories', 'total_reviews', 'average_rating',
            'distance_km', 'competition_score', 'is_active', 'created_at',
            'updated_at', 'last_analyzed_at'
        ]
        read_only_fields = [
            'id', 'business_name', 'created_at', 'updated_at', 'last_analyzed_at'
        ]


class ReviewSerializer(serializers.ModelSerializer):
    """Serializer for Review model"""
    
    business_name = serializers.CharField(source='business_profile.business_name', read_only=True)
    
    class Meta:
        model = Review
        fields = [
            'id', 'business_profile', 'business_name', 'google_review_id',
            'reviewer_name', 'reviewer_photo_url', 'rating', 'review_text',
            'review_date', 'business_response', 'response_date', 'sentiment',
            'sentiment_score', 'key_topics', 'is_flagged', 'flagged_reason',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'business_name', 'google_review_id', 'sentiment',
            'sentiment_score', 'key_topics', 'created_at', 'updated_at'
        ]


class LocalAuditReportSerializer(serializers.ModelSerializer):
    """Serializer for LocalAuditReport model"""
    
    business_name = serializers.CharField(source='business_profile.business_name', read_only=True)
    tasks_count = serializers.IntegerField(source='tasks.count', read_only=True)
    
    class Meta:
        model = LocalAuditReport
        fields = [
            'id', 'business_profile', 'business_name', 'audit_date', 'status',
            'overall_score', 'nap_consistency_score', 'profile_completeness_score',
            'review_health_score', 'local_visibility_score', 'audit_results',
            'recommendations', 'ai_summary', 'priority_tasks', 'tasks_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'business_name', 'audit_results', 'recommendations',
            'ai_summary', 'priority_tasks', 'tasks_count', 'created_at', 'updated_at'
        ]


class LocalAuditReportDetailSerializer(LocalAuditReportSerializer):
    """Detailed serializer for LocalAuditReport with related tasks"""
    
    tasks = serializers.SerializerMethodField()
    
    class Meta(LocalAuditReportSerializer.Meta):
        fields = LocalAuditReportSerializer.Meta.fields + ['tasks']
    
    def get_tasks(self, obj):
        """Get related SEO tasks"""
        tasks = obj.tasks.all()[:10]  # Limit to 10 most recent tasks
        return LocalSEOTaskSerializer(tasks, many=True).data


class SentimentTagSerializer(serializers.ModelSerializer):
    """Serializer for SentimentTag model"""
    
    business_name = serializers.CharField(source='business_profile.business_name', read_only=True)
    
    class Meta:
        model = SentimentTag
        fields = [
            'id', 'business_profile', 'business_name', 'tag_name', 'tag_type',
            'description', 'mention_count', 'sentiment_score', 'first_mentioned',
            'last_mentioned', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'business_name', 'mention_count', 'sentiment_score',
            'first_mentioned', 'last_mentioned', 'created_at', 'updated_at'
        ]


class LocalSEOTaskSerializer(serializers.ModelSerializer):
    """Serializer for LocalSEOTask model"""
    
    business_name = serializers.CharField(source='business_profile.business_name', read_only=True)
    assigned_to_email = serializers.CharField(source='assigned_to.email', read_only=True)
    audit_report_date = serializers.DateTimeField(source='audit_report.audit_date', read_only=True)
    
    class Meta:
        model = LocalSEOTask
        fields = [
            'id', 'business_profile', 'business_name', 'audit_report',
            'audit_report_date', 'title', 'description', 'category',
            'priority', 'status', 'estimated_impact', 'estimated_effort',
            'assigned_to', 'assigned_to_email', 'due_date', 'completed_at',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'business_name', 'audit_report_date', 'assigned_to_email',
            'created_at', 'updated_at'
        ]


class BusinessProfileSummarySerializer(serializers.ModelSerializer):
    """Summary serializer for BusinessProfile (for lists)"""
    
    recent_audit_score = serializers.SerializerMethodField()
    pending_tasks_count = serializers.SerializerMethodField()
    
    class Meta:
        model = BusinessProfile
        fields = [
            'id', 'business_name', 'city', 'state', 'status', 'is_verified',
            'total_reviews', 'average_rating', 'profile_completeness_score',
            'recent_audit_score', 'pending_tasks_count', 'created_at', 'updated_at'
        ]
    
    def get_recent_audit_score(self, obj):
        """Get the most recent audit overall score"""
        recent_audit = obj.audit_reports.filter(status='completed').first()
        return recent_audit.overall_score if recent_audit else None
    
    def get_pending_tasks_count(self, obj):
        """Get count of pending SEO tasks"""
        return obj.seo_tasks.filter(status='pending').count()


class KeywordRankingTrendSerializer(serializers.Serializer):
    """Serializer for keyword ranking trends"""
    
    keyword = serializers.CharField()
    target_location = serializers.CharField()
    rankings = serializers.ListField(
        child=serializers.DictField()
    )
    trend_direction = serializers.CharField()  # 'up', 'down', 'stable'
    position_change = serializers.IntegerField()


class CompetitorAnalysisSerializer(serializers.Serializer):
    """Serializer for competitor analysis results"""
    
    business_profile = BusinessProfileSummarySerializer()
    competitors = CompetitorProfileSerializer(many=True)
    competitive_metrics = serializers.DictField()
    recommendations = serializers.ListField(
        child=serializers.CharField()
    )
