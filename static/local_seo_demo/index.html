<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Local SEO Intelligence Demo - SEOAnalyser.com.au</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .score-ring {
            position: relative;
            width: 120px;
            height: 120px;
        }
        .score-ring svg {
            transform: rotate(-90deg);
        }
        .score-ring .score-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.5rem;
            font-weight: bold;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .tooltip {
            position: relative;
            cursor: help;
        }
        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
        .tooltip-text {
            visibility: hidden;
            opacity: 0;
            width: 200px;
            background-color: #333;
            color: white;
            text-align: center;
            border-radius: 6px;
            padding: 8px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            transition: opacity 0.3s;
            font-size: 0.875rem;
        }
        .tooltip-text::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #333 transparent transparent transparent;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900">Local SEO Intelligence</h1>
                    <span class="ml-3 px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">Demo</span>
                </div>
                <div class="flex items-center space-x-4">
                    <select id="businessSelect" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option value="">Loading businesses...</option>
                    </select>
                    <button id="refreshBtn" class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="bg-white border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex space-x-8">
                <a href="#dashboard" class="nav-link active border-b-2 border-blue-500 py-4 px-1 text-sm font-medium text-blue-600">
                    <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                </a>
                <a href="#profile" class="nav-link border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700">
                    <i class="fas fa-building mr-2"></i>Business Profile
                </a>
                <a href="#keywords" class="nav-link border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700">
                    <i class="fas fa-search mr-2"></i>Keywords
                </a>
                <a href="#competitors" class="nav-link border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700">
                    <i class="fas fa-users mr-2"></i>Competitors
                </a>
                <a href="#reviews" class="nav-link border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700">
                    <i class="fas fa-star mr-2"></i>Reviews
                </a>
                <a href="#tasks" class="nav-link border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700">
                    <i class="fas fa-tasks mr-2"></i>Tasks
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Dashboard Section -->
        <div id="dashboard" class="section active">
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">Local SEO Dashboard</h2>
                <p class="text-gray-600">Comprehensive overview of your local search performance</p>
            </div>

            <!-- Overall Score -->
            <div class="bg-white rounded-lg shadow p-6 mb-8">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">Overall Local SEO Score</h3>
                    <div class="tooltip">
                        <i class="fas fa-info-circle text-gray-400"></i>
                        <span class="tooltip-text">Weighted composite score based on NAP consistency, profile completeness, review health, and local visibility</span>
                    </div>
                </div>
                <div class="flex items-center justify-center">
                    <div class="score-ring">
                        <svg width="120" height="120">
                            <circle cx="60" cy="60" r="50" stroke="#e5e7eb" stroke-width="8" fill="none"/>
                            <circle id="overallScoreCircle" cx="60" cy="60" r="50" stroke="#3b82f6" stroke-width="8" 
                                    fill="none" stroke-dasharray="314" stroke-dashoffset="314" 
                                    style="transition: stroke-dashoffset 1s ease-in-out"/>
                        </svg>
                        <div class="score-text" id="overallScoreText">--</div>
                    </div>
                </div>
            </div>

            <!-- Sub-Scores Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- NAP Consistency -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="text-sm font-medium text-gray-900">NAP Consistency</h4>
                        <div class="tooltip">
                            <i class="fas fa-info-circle text-gray-400"></i>
                            <span class="tooltip-text">Name, Address, Phone consistency across platforms</span>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <div class="text-2xl font-bold text-gray-900" id="napScore">--</div>
                        <div class="ml-2 text-sm text-gray-500">/100</div>
                    </div>
                    <div class="mt-2">
                        <div class="bg-gray-200 rounded-full h-2">
                            <div id="napProgress" class="bg-green-500 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <!-- Profile Completeness -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="text-sm font-medium text-gray-900">Profile Completeness</h4>
                        <div class="tooltip">
                            <i class="fas fa-info-circle text-gray-400"></i>
                            <span class="tooltip-text">Google Business Profile completeness score</span>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <div class="text-2xl font-bold text-gray-900" id="completenessScore">--</div>
                        <div class="ml-2 text-sm text-gray-500">/100</div>
                    </div>
                    <div class="mt-2">
                        <div class="bg-gray-200 rounded-full h-2">
                            <div id="completenessProgress" class="bg-blue-500 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <!-- Review Health -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="text-sm font-medium text-gray-900">Review Health</h4>
                        <div class="tooltip">
                            <i class="fas fa-info-circle text-gray-400"></i>
                            <span class="tooltip-text">Review volume, rating, and sentiment analysis</span>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <div class="text-2xl font-bold text-gray-900" id="reviewScore">--</div>
                        <div class="ml-2 text-sm text-gray-500">/100</div>
                    </div>
                    <div class="mt-2">
                        <div class="bg-gray-200 rounded-full h-2">
                            <div id="reviewProgress" class="bg-yellow-500 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <!-- Local Visibility -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="text-sm font-medium text-gray-900">Local Visibility</h4>
                        <div class="tooltip">
                            <i class="fas fa-info-circle text-gray-400"></i>
                            <span class="tooltip-text">Keyword ranking performance in local search</span>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <div class="text-2xl font-bold text-gray-900" id="visibilityScore">--</div>
                        <div class="ml-2 text-sm text-gray-500">/100</div>
                    </div>
                    <div class="mt-2">
                        <div class="bg-gray-200 rounded-full h-2">
                            <div id="visibilityProgress" class="bg-purple-500 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-star text-yellow-500 text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-500">Average Rating</div>
                            <div class="text-2xl font-bold text-gray-900" id="avgRating">--</div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-comments text-blue-500 text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-500">Total Reviews</div>
                            <div class="text-2xl font-bold text-gray-900" id="totalReviews">--</div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-tasks text-green-500 text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-500">Pending Tasks</div>
                            <div class="text-2xl font-bold text-gray-900" id="pendingTasks">--</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Other sections will be loaded dynamically -->
        <div id="profile" class="section hidden">
            <div class="loading-placeholder">
                <div class="loading"></div>
                <span class="ml-2">Loading business profile...</span>
            </div>
        </div>

        <div id="keywords" class="section hidden">
            <div class="loading-placeholder">
                <div class="loading"></div>
                <span class="ml-2">Loading keyword data...</span>
            </div>
        </div>

        <div id="competitors" class="section hidden">
            <div class="loading-placeholder">
                <div class="loading"></div>
                <span class="ml-2">Loading competitor analysis...</span>
            </div>
        </div>

        <div id="reviews" class="section hidden">
            <div class="loading-placeholder">
                <div class="loading"></div>
                <span class="ml-2">Loading review intelligence...</span>
            </div>
        </div>

        <div id="tasks" class="section hidden">
            <div class="loading-placeholder">
                <div class="loading"></div>
                <span class="ml-2">Loading task board...</span>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-500">
                    Local SEO Intelligence Demo - SEOAnalyser.com.au
                </div>
                <div class="text-sm text-gray-500">
                    API Status: <span id="apiStatus" class="text-green-600">Connected</span>
                </div>
            </div>
        </div>
    </footer>

    <script src="app.js"></script>
</body>
</html>
