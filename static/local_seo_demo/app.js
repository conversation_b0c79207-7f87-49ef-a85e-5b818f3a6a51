// Local SEO Intelligence Demo App
class LocalSEODemo {
    constructor() {
        this.baseURL = this.detectAPIBaseURL();
        this.currentBusinessId = null;
        this.authToken = null;
        this.init();
    }

    detectAPIBaseURL() {
        // Auto-detect based on current location
        const hostname = window.location.hostname;
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            return 'http://localhost:8000';
        }
        return window.location.origin;
    }

    async init() {
        this.setupEventListeners();
        await this.loadBusinessProfiles();
        this.showSection('dashboard');
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.getAttribute('href').substring(1);
                this.showSection(section);
            });
        });

        // Business selector
        document.getElementById('businessSelect').addEventListener('change', (e) => {
            this.currentBusinessId = e.target.value;
            if (this.currentBusinessId) {
                this.loadDashboardData();
            }
        });

        // Refresh button
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.refreshCurrentSection();
        });
    }

    async makeAPIRequest(endpoint, options = {}) {
        try {
            const url = `${this.baseURL}/api/local-seo${endpoint}`;
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };

            if (this.authToken) {
                headers['Authorization'] = `Bearer ${this.authToken}`;
            }

            const response = await fetch(url, {
                ...options,
                headers
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API Request failed:', error);
            this.updateAPIStatus('error');
            throw error;
        }
    }

    updateAPIStatus(status) {
        const statusElement = document.getElementById('apiStatus');
        if (status === 'error') {
            statusElement.textContent = 'Connection Error';
            statusElement.className = 'text-red-600';
        } else {
            statusElement.textContent = 'Connected';
            statusElement.className = 'text-green-600';
        }
    }

    async loadBusinessProfiles() {
        try {
            const data = await this.makeAPIRequest('/profiles/');
            const select = document.getElementById('businessSelect');

            select.innerHTML = '<option value="">Select a business...</option>';

            if (data.results && data.results.length > 0) {
                data.results.forEach(profile => {
                    const option = document.createElement('option');
                    option.value = profile.id;
                    option.textContent = `${profile.business_name} - ${profile.city}, ${profile.state}`;
                    select.appendChild(option);
                });

                // Auto-select first business
                this.currentBusinessId = data.results[0].id;
                select.value = this.currentBusinessId;
                await this.loadDashboardData();
            } else {
                select.innerHTML = '<option value="">No businesses found</option>';
            }
        } catch (error) {
            console.error('Failed to load business profiles:', error);
            document.getElementById('businessSelect').innerHTML = '<option value="">Error loading businesses</option>';
        }
    }

    async loadDashboardData() {
        if (!this.currentBusinessId) return;

        try {
            // Load latest audit report
            const auditsData = await this.makeAPIRequest(`/profiles/${this.currentBusinessId}/audits/`);

            if (auditsData.results && auditsData.results.length > 0) {
                const latestAudit = auditsData.results[0];
                this.updateDashboardScores(latestAudit);
            }

            // Load business profile for additional stats
            const profileData = await this.makeAPIRequest(`/profiles/${this.currentBusinessId}/`);
            this.updateQuickStats(profileData);

            // Load pending tasks count
            const tasksData = await this.makeAPIRequest(`/profiles/${this.currentBusinessId}/tasks/?status=pending`);
            document.getElementById('pendingTasks').textContent = tasksData.results ? tasksData.results.length : 0;

        } catch (error) {
            console.error('Failed to load dashboard data:', error);
        }
    }

    updateDashboardScores(audit) {
        // Update overall score ring
        const overallScore = audit.overall_score || 0;
        this.updateScoreRing(overallScore);

        // Update sub-scores
        this.updateSubScore('nap', audit.nap_consistency_score || 0);
        this.updateSubScore('completeness', audit.profile_completeness_score || 0);
        this.updateSubScore('review', audit.review_health_score || 0);
        this.updateSubScore('visibility', audit.local_visibility_score || 0);
    }

    updateScoreRing(score) {
        const circle = document.getElementById('overallScoreCircle');
        const text = document.getElementById('overallScoreText');

        const circumference = 2 * Math.PI * 50; // radius = 50
        const offset = circumference - (score / 100) * circumference;

        circle.style.strokeDashoffset = offset;
        text.textContent = score;

        // Update color based on score
        if (score >= 80) {
            circle.style.stroke = '#10b981'; // green
        } else if (score >= 60) {
            circle.style.stroke = '#f59e0b'; // yellow
        } else {
            circle.style.stroke = '#ef4444'; // red
        }
    }

    updateSubScore(type, score) {
        document.getElementById(`${type}Score`).textContent = score;
        document.getElementById(`${type}Progress`).style.width = `${score}%`;
    }

    updateQuickStats(profile) {
        document.getElementById('avgRating').textContent = profile.average_rating || '--';
        document.getElementById('totalReviews').textContent = profile.total_reviews || 0;
    }

    showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.section').forEach(section => {
            section.classList.add('hidden');
            section.classList.remove('active');
        });

        // Show selected section
        const targetSection = document.getElementById(sectionName);
        if (targetSection) {
            targetSection.classList.remove('hidden');
            targetSection.classList.add('active');
        }

        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active', 'border-blue-500', 'text-blue-600');
            link.classList.add('border-transparent', 'text-gray-500');
        });

        const activeLink = document.querySelector(`[href="#${sectionName}"]`);
        if (activeLink) {
            activeLink.classList.add('active', 'border-blue-500', 'text-blue-600');
            activeLink.classList.remove('border-transparent', 'text-gray-500');
        }

        // Load section-specific data
        this.loadSectionData(sectionName);
    }

    async loadSectionData(sectionName) {
        if (!this.currentBusinessId) return;

        const section = document.getElementById(sectionName);

        try {
            switch (sectionName) {
                case 'profile':
                    await this.loadProfileSection(section);
                    break;
                case 'keywords':
                    await this.loadKeywordsSection(section);
                    break;
                case 'competitors':
                    await this.loadCompetitorsSection(section);
                    break;
                case 'reviews':
                    await this.loadReviewsSection(section);
                    break;
                case 'tasks':
                    await this.loadTasksSection(section);
                    break;
            }
        } catch (error) {
            console.error(`Failed to load ${sectionName} section:`, error);
            section.innerHTML = `
                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                    <div class="flex">
                        <i class="fas fa-exclamation-triangle text-red-400 mr-3 mt-1"></i>
                        <div>
                            <h3 class="text-sm font-medium text-red-800">Error Loading Data</h3>
                            <p class="text-sm text-red-700 mt-1">Failed to load ${sectionName} data. Please try again.</p>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    async loadProfileSection(section) {
        const profileData = await this.makeAPIRequest(`/profiles/${this.currentBusinessId}/`);

        section.innerHTML = `
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">Business Profile</h2>
                <p class="text-gray-600">Manage your business information and Google Business Profile details</p>
            </div>

            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Business Information</h3>
                </div>
                <div class="px-6 py-4">
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Business Name</dt>
                            <dd class="mt-1 text-sm text-gray-900">${profileData.business_name || 'Not set'}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Business Type</dt>
                            <dd class="mt-1 text-sm text-gray-900">${profileData.business_type || 'Not set'}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Address</dt>
                            <dd class="mt-1 text-sm text-gray-900">${profileData.full_address || 'Not set'}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Phone Number</dt>
                            <dd class="mt-1 text-sm text-gray-900">${profileData.phone_number || 'Not set'}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Website</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                ${profileData.website_url ? `<a href="${profileData.website_url}" target="_blank" class="text-blue-600 hover:text-blue-800">${profileData.website_url}</a>` : 'Not set'}
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Primary Category</dt>
                            <dd class="mt-1 text-sm text-gray-900">${profileData.primary_category || 'Not set'}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Verification Status</dt>
                            <dd class="mt-1">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${profileData.is_verified ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                    ${profileData.is_verified ? 'Verified' : 'Not Verified'}
                                </span>
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Profile Completeness</dt>
                            <dd class="mt-1 text-sm text-gray-900">${profileData.profile_completeness_score || 0}%</dd>
                        </div>
                    </dl>
                </div>
            </div>
        `;
    }

    async loadKeywordsSection(section) {
        const keywordsData = await this.makeAPIRequest(`/profiles/${this.currentBusinessId}/keywords/`);

        section.innerHTML = `
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">Keyword Visibility</h2>
                <p class="text-gray-600">Track your local keyword rankings and search visibility</p>
            </div>

            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Tracked Keywords</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Keyword</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Search Volume</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Competition</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            ${keywordsData.results ? keywordsData.results.map(keyword => `
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${keyword.keyword}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${keyword.target_location}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${this.formatNumber(keyword.search_volume)}</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${this.getCompetitionColor(keyword.competition_level)}">
                                            ${keyword.competition_level}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${keyword.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                            ${keyword.is_active ? 'Active' : 'Inactive'}
                                        </span>
                                    </td>
                                </tr>
                            `).join('') : '<tr><td colspan="5" class="px-6 py-4 text-center text-gray-500">No keywords found</td></tr>'}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    async loadCompetitorsSection(section) {
        const competitorsData = await this.makeAPIRequest(`/profiles/${this.currentBusinessId}/competitors/`);

        section.innerHTML = `
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">Competitor Analysis</h2>
                <p class="text-gray-600">Compare your business with local competitors</p>
            </div>

            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Local Competitors</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Business Name</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Distance</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rating</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reviews</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Competition Score</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            ${competitorsData.competitors ? competitorsData.competitors.map(competitor => `
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">${competitor.competitor_name}</div>
                                        <div class="text-sm text-gray-500">${competitor.primary_category || 'N/A'}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${competitor.distance_km} km</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <span class="text-sm text-gray-900">${competitor.average_rating}</span>
                                            <i class="fas fa-star text-yellow-400 ml-1"></i>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${this.formatNumber(competitor.total_reviews)}</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                                <div class="bg-blue-600 h-2 rounded-full" style="width: ${competitor.competition_score}%"></div>
                                            </div>
                                            <span class="text-sm text-gray-900">${competitor.competition_score}</span>
                                        </div>
                                    </td>
                                </tr>
                            `).join('') : '<tr><td colspan="5" class="px-6 py-4 text-center text-gray-500">No competitors found</td></tr>'}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    async loadReviewsSection(section) {
        const reviewsData = await this.makeAPIRequest(`/profiles/${this.currentBusinessId}/reviews/`);

        section.innerHTML = `
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">Review Intelligence</h2>
                <p class="text-gray-600">AI-powered analysis of customer reviews and sentiment</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Sentiment Overview</h3>
                    <canvas id="sentimentChart" width="200" height="200"></canvas>
                </div>

                <div class="lg:col-span-2 bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Review Summary</h3>
                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600" id="positiveCount">0</div>
                            <div class="text-sm text-gray-500">Positive</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-yellow-600" id="neutralCount">0</div>
                            <div class="text-sm text-gray-500">Neutral</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-red-600" id="negativeCount">0</div>
                            <div class="text-sm text-gray-500">Negative</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Recent Reviews</h3>
                </div>
                <div class="divide-y divide-gray-200">
                    ${reviewsData.reviews ? reviewsData.reviews.slice(0, 10).map(review => `
                        <div class="px-6 py-4">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <span class="font-medium text-gray-900">${review.reviewer_name}</span>
                                        <div class="flex items-center ml-2">
                                            ${Array.from({length: 5}, (_, i) => `
                                                <i class="fas fa-star ${i < review.rating ? 'text-yellow-400' : 'text-gray-300'} text-sm"></i>
                                            `).join('')}
                                        </div>
                                        <span class="ml-2 text-sm text-gray-500">${this.formatDate(review.review_date)}</span>
                                    </div>
                                    <p class="text-gray-700 text-sm">${review.review_text || 'No review text'}</p>
                                    ${review.key_topics && review.key_topics.length > 0 ? `
                                        <div class="mt-2">
                                            ${review.key_topics.map(topic => `
                                                <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mr-1">${topic}</span>
                                            `).join('')}
                                        </div>
                                    ` : ''}
                                </div>
                                <div class="ml-4">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${this.getSentimentColor(review.sentiment)}">
                                        ${review.sentiment || 'Unknown'}
                                    </span>
                                </div>
                            </div>
                        </div>
                    `).join('') : '<div class="px-6 py-4 text-center text-gray-500">No reviews found</div>'}
                </div>
            </div>
        `;

        // Create sentiment chart if reviews exist
        if (reviewsData.reviews && reviewsData.reviews.length > 0) {
            this.createSentimentChart(reviewsData.reviews);
        }
    }

    async loadTasksSection(section) {
        const tasksData = await this.makeAPIRequest(`/profiles/${this.currentBusinessId}/tasks/`);

        const tasksByStatus = {
            pending: [],
            'in_progress': [],
            completed: [],
            ignored: []
        };

        if (tasksData.results) {
            tasksData.results.forEach(task => {
                if (tasksByStatus[task.status]) {
                    tasksByStatus[task.status].push(task);
                }
            });
        }

        section.innerHTML = `
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">SEO Tasks & Progress</h2>
                <p class="text-gray-600">Track and manage your local SEO improvement tasks</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                ${Object.entries(tasksByStatus).map(([status, tasks]) => `
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-4 py-3 border-b border-gray-200">
                            <h3 class="text-sm font-semibold text-gray-900 uppercase tracking-wider">
                                ${status.replace('_', ' ')} (${tasks.length})
                            </h3>
                        </div>
                        <div class="p-4 space-y-3 max-h-96 overflow-y-auto">
                            ${tasks.map(task => `
                                <div class="border border-gray-200 rounded-lg p-3 task-card" data-task-id="${task.id}">
                                    <div class="flex items-start justify-between mb-2">
                                        <h4 class="text-sm font-medium text-gray-900 line-clamp-2">${task.title}</h4>
                                        <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium ${this.getPriorityColor(task.priority)}">
                                            ${task.priority}
                                        </span>
                                    </div>
                                    <p class="text-xs text-gray-600 mb-2 line-clamp-3">${task.description}</p>
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-gray-500">${task.category}</span>
                                        ${status === 'pending' ? `
                                            <button onclick="window.localSEODemo.updateTaskStatus('${task.id}', 'completed')"
                                                    class="text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700">
                                                Complete
                                            </button>
                                        ` : ''}
                                    </div>
                                </div>
                            `).join('')}
                            ${tasks.length === 0 ? '<div class="text-center text-gray-500 text-sm py-4">No tasks</div>' : ''}
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    async updateTaskStatus(taskId, newStatus) {
        try {
            await this.makeAPIRequest(`/tasks/${taskId}/`, {
                method: 'PATCH',
                body: JSON.stringify({ status: newStatus })
            });

            // Reload tasks section
            this.loadSectionData('tasks');
        } catch (error) {
            console.error('Failed to update task status:', error);
            alert('Failed to update task status. Please try again.');
        }
    }

    refreshCurrentSection() {
        const activeSection = document.querySelector('.section.active');
        if (activeSection) {
            const sectionName = activeSection.id;
            if (sectionName === 'dashboard') {
                this.loadDashboardData();
            } else {
                this.loadSectionData(sectionName);
            }
        }
    }

    // Utility methods
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-AU', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    getCompetitionColor(level) {
        const colors = {
            low: 'bg-green-100 text-green-800',
            medium: 'bg-yellow-100 text-yellow-800',
            high: 'bg-red-100 text-red-800'
        };
        return colors[level] || 'bg-gray-100 text-gray-800';
    }

    getSentimentColor(sentiment) {
        const colors = {
            positive: 'bg-green-100 text-green-800',
            neutral: 'bg-yellow-100 text-yellow-800',
            negative: 'bg-red-100 text-red-800'
        };
        return colors[sentiment] || 'bg-gray-100 text-gray-800';
    }

    getPriorityColor(priority) {
        const colors = {
            low: 'bg-gray-100 text-gray-800',
            medium: 'bg-yellow-100 text-yellow-800',
            high: 'bg-orange-100 text-orange-800',
            critical: 'bg-red-100 text-red-800'
        };
        return colors[priority] || 'bg-gray-100 text-gray-800';
    }

    createSentimentChart(reviews) {
        const sentimentCounts = {
            positive: 0,
            neutral: 0,
            negative: 0
        };

        reviews.forEach(review => {
            if (review.sentiment && sentimentCounts.hasOwnProperty(review.sentiment)) {
                sentimentCounts[review.sentiment]++;
            }
        });

        // Update counts in the UI
        document.getElementById('positiveCount').textContent = sentimentCounts.positive;
        document.getElementById('neutralCount').textContent = sentimentCounts.neutral;
        document.getElementById('negativeCount').textContent = sentimentCounts.negative;

        // Create pie chart
        const ctx = document.getElementById('sentimentChart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Positive', 'Neutral', 'Negative'],
                datasets: [{
                    data: [sentimentCounts.positive, sentimentCounts.neutral, sentimentCounts.negative],
                    backgroundColor: ['#10b981', '#f59e0b', '#ef4444'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.localSEODemo = new LocalSEODemo();
});
