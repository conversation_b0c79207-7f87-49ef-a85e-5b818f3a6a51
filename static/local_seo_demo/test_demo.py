#!/usr/bin/env python3
"""
Local SEO Intelligence Demo Test Script

This script tests all the API endpoints used by the demo to ensure
they're working correctly and returning expected data.
"""

import requests
import json
import sys
from datetime import datetime


class LocalSEODemoTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.api_base = f"{base_url}/api/local-seo"
        self.business_id = None
        self.test_results = []
    
    def log_test(self, test_name, success, message=""):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat()
        })
        print(f"{status} {test_name}: {message}")
    
    def test_endpoint(self, endpoint, method="GET", data=None, expected_keys=None):
        """Test a single API endpoint"""
        try:
            url = f"{self.api_base}{endpoint}"
            
            if method == "GET":
                response = requests.get(url, timeout=10)
            elif method == "POST":
                response = requests.post(url, json=data, timeout=10)
            elif method == "PATCH":
                response = requests.patch(url, json=data, timeout=10)
            
            if response.status_code in [200, 201]:
                result = response.json()
                
                # Check for expected keys if provided
                if expected_keys:
                    missing_keys = [key for key in expected_keys if key not in result]
                    if missing_keys:
                        return False, f"Missing keys: {missing_keys}"
                
                return True, f"Status {response.status_code}, {len(str(result))} chars"
            else:
                return False, f"HTTP {response.status_code}: {response.text[:100]}"
                
        except requests.exceptions.RequestException as e:
            return False, f"Request failed: {str(e)}"
        except json.JSONDecodeError as e:
            return False, f"Invalid JSON: {str(e)}"
    
    def run_all_tests(self):
        """Run all demo API tests"""
        print("🚀 Starting Local SEO Demo API Tests")
        print("=" * 50)
        
        # Test 1: List Business Profiles
        success, message = self.test_endpoint(
            "/profiles/",
            expected_keys=['results']
        )
        self.log_test("List Business Profiles", success, message)
        
        if success:
            # Get first business ID for subsequent tests
            response = requests.get(f"{self.api_base}/profiles/")
            data = response.json()
            if data.get('results'):
                self.business_id = data['results'][0]['id']
                self.log_test("Extract Business ID", True, f"ID: {self.business_id}")
            else:
                self.log_test("Extract Business ID", False, "No businesses found")
                return
        
        if not self.business_id:
            print("❌ Cannot continue tests without business ID")
            return
        
        # Test 2: Business Profile Details
        success, message = self.test_endpoint(
            f"/profiles/{self.business_id}/",
            expected_keys=['business_name', 'city', 'state']
        )
        self.log_test("Business Profile Details", success, message)
        
        # Test 3: Dashboard Overview
        success, message = self.test_endpoint(
            "/dashboard/",
            expected_keys=['total_profiles', 'total_reviews']
        )
        self.log_test("Dashboard Overview", success, message)
        
        # Test 4: Audit Reports
        success, message = self.test_endpoint(
            f"/profiles/{self.business_id}/audits/",
            expected_keys=['results']
        )
        self.log_test("Audit Reports", success, message)
        
        # Test 5: Keywords
        success, message = self.test_endpoint(
            f"/profiles/{self.business_id}/keywords/",
            expected_keys=['results']
        )
        self.log_test("Keywords List", success, message)
        
        # Test 6: SEO Tasks
        success, message = self.test_endpoint(
            f"/profiles/{self.business_id}/tasks/",
            expected_keys=['results']
        )
        self.log_test("SEO Tasks", success, message)
        
        # Test 7: Demo Page Access
        try:
            demo_url = f"{self.base_url}/api/local-seo/demo/"
            response = requests.get(demo_url, timeout=10)
            if response.status_code == 200 and "Local SEO Intelligence" in response.text:
                self.log_test("Demo Page Access", True, f"Status {response.status_code}")
            else:
                self.log_test("Demo Page Access", False, f"Status {response.status_code}")
        except Exception as e:
            self.log_test("Demo Page Access", False, str(e))
        
        # Test 8: Demo JavaScript
        try:
            js_url = f"{self.base_url}/api/local-seo/demo/app.js"
            response = requests.get(js_url, timeout=10)
            if response.status_code == 200 and "LocalSEODemo" in response.text:
                self.log_test("Demo JavaScript", True, f"Status {response.status_code}")
            else:
                self.log_test("Demo JavaScript", False, f"Status {response.status_code}")
        except Exception as e:
            self.log_test("Demo JavaScript", False, str(e))
        
        # Print Summary
        self.print_summary()
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  • {result['test']}: {result['message']}")
        
        print("\n🎯 DEMO ACCESS:")
        print(f"  • Demo URL: {self.base_url}/api/local-seo/demo/")
        print(f"  • API Base: {self.api_base}")
        
        if self.business_id:
            print(f"  • Test Business ID: {self.business_id}")
        
        print("\n✨ Demo is ready for testing!" if failed_tests == 0 else "\n⚠️  Some issues found - check failed tests above")


def main():
    """Main function"""
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:8000"
    
    print(f"Testing Local SEO Demo at: {base_url}")
    
    tester = LocalSEODemoTester(base_url)
    tester.run_all_tests()


if __name__ == "__main__":
    main()
